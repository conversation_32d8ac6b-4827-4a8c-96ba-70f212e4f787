#!/usr/bin/env python3
"""
Teste de Elegibilidade de Trainers
Verifica por que 0 trainers estão sendo considerados elegíveis
"""

import os

def TestTrainerEligibility():
    """Testa a elegibilidade de trainers específicos"""
    
    print("🔍 TESTE DE ELEGIBILIDADE DE TRAINERS")
    print("=" * 50)
    
    # Verificar configuração
    config_file = "include/wild_encounters_config.h"
    
    if not os.path.exists(config_file):
        print(f"❌ Arquivo de configuração não encontrado: {config_file}")
        return
    
    with open(config_file, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    print("📊 CONFIGURAÇÕES ATUAIS:")
    
    # Verificar configurações principais
    configs = [
        "TRAINER_RANDOMIZATION_ENABLED",
        "RANDOMIZE_REGULAR_TRAINERS",
        "RANDOMIZE_IMPORTANT_TRAINERS", 
        "RANDOMIZE_BOSS_TRAINERS"
    ]
    
    for config in configs:
        if config in content:
            # Extrair valor
            lines = content.split('\n')
            for line in lines:
                if config in line and '#define' in line:
                    print(f"   {config}: {line.strip()}")
                    break
        else:
            print(f"   {config}: NÃO ENCONTRADO")
    
    print()
    
    # Testar trainers específicos
    test_trainers = [
        (1, 2, "Regular Trainer"),
        (418, 84, "Koga (Boss)"),
        (419, 84, "Sabrina (Boss)"),
        (420, 84, "Blaine (Boss)"),
        (350, 27, "Giovanni (Boss)"),
        (328, 42, "Rival (Important)")
    ]
    
    print("🎯 TESTE DE TRAINERS ESPECÍFICOS:")
    
    for trainer_id, trainer_class, description in test_trainers:
        eligible = IsTrainerEligibleForRandomization(trainer_class, trainer_id, content)
        status = "✅ ELEGÍVEL" if eligible else "❌ NÃO ELEGÍVEL"
        print(f"   Trainer {trainer_id:3d} ({description}): {status}")
        
        # Mostrar detalhes da análise
        details = AnalyzeTrainerEligibility(trainer_class, trainer_id, content)
        for detail in details:
            print(f"      - {detail}")
    
    return True

def IsTrainerEligibleForRandomization(trainer_class, trainer_id, content):
    """Versão de teste da função de elegibilidade"""
    
    # Gym Leaders (IDs específicos)
    gym_leader_ids = [0x19E, 0x19F, 0x1A0, 0x1A1, 0x1A2, 0x1A4, 0x1A3, 0x15E]
    
    # Elite Four Round 1 e Round 2
    elite_four_ids = [
        0x19A, 0x19B, 0x19C, 0x19D,  # Round 1
        0x2DF, 0x2E0, 0x2E1, 0x2E2   # Round 2
    ]
    
    # Giovanni adicional
    giovanni_ids = [0x15C, 0x15D]
    
    # Rival IDs
    rival_ids = [
        0x148, 0x146, 0x147,  # RIVAL1
        0x14B, 0x149, 0x14A,  # RIVAL2
        0x14E, 0x14C, 0x14D,  # RIVAL3
        0x1A9, 0x1A7, 0x1A8,  # RIVAL4
        0x1AF, 0x1AD, 0x1AE,  # RIVAL5
        0x1B2, 0x1B0, 0x1B1,  # RIVAL6
        0x1B5, 0x1B3, 0x1B4,  # RIVAL7
        0x1B8, 0x1B6, 0x1B7,  # RIVAL8
        0x2E5, 0x2E3, 0x2E4   # RIVAL9
    ]
    
    # Verificar se deve randomizar bosses
    randomize_bosses = "RANDOMIZE_BOSS_TRAINERS TRUE" in content
    if (trainer_id in gym_leader_ids or trainer_id in elite_four_ids or trainer_id in giovanni_ids) and not randomize_bosses:
        return False
    
    # Verificar se deve randomizar rivais
    randomize_important = "RANDOMIZE_IMPORTANT_TRAINERS TRUE" in content
    if trainer_id in rival_ids and not randomize_important:
        return False
    
    # Verificar se deve randomizar treinadores regulares
    randomize_regular = "RANDOMIZE_REGULAR_TRAINERS TRUE" in content
    
    # Se não é boss nem rival, é treinador regular
    is_regular = (trainer_id not in gym_leader_ids and
                 trainer_id not in elite_four_ids and
                 trainer_id not in giovanni_ids and
                 trainer_id not in rival_ids)
    
    if is_regular:
        return randomize_regular
    
    return True

def AnalyzeTrainerEligibility(trainer_class, trainer_id, content):
    """Analisa detalhadamente por que um trainer é ou não elegível"""
    
    details = []
    
    # Gym Leaders
    gym_leader_ids = [0x19E, 0x19F, 0x1A0, 0x1A1, 0x1A2, 0x1A4, 0x1A3, 0x15E]
    elite_four_ids = [0x19A, 0x19B, 0x19C, 0x19D, 0x2DF, 0x2E0, 0x2E1, 0x2E2]
    giovanni_ids = [0x15C, 0x15D]
    rival_ids = [
        0x148, 0x146, 0x147, 0x14B, 0x149, 0x14A, 0x14E, 0x14C, 0x14D,
        0x1A9, 0x1A7, 0x1A8, 0x1AF, 0x1AD, 0x1AE, 0x1B2, 0x1B0, 0x1B1,
        0x1B5, 0x1B3, 0x1B4, 0x1B8, 0x1B6, 0x1B7, 0x2E5, 0x2E3, 0x2E4
    ]
    
    # Identificar categoria
    if trainer_id in gym_leader_ids:
        details.append("Categoria: GYM LEADER")
        randomize_bosses = "RANDOMIZE_BOSS_TRAINERS TRUE" in content
        details.append(f"RANDOMIZE_BOSS_TRAINERS: {randomize_bosses}")
        if not randomize_bosses:
            details.append("BLOQUEADO: Boss trainers desabilitados")
    elif trainer_id in elite_four_ids:
        details.append("Categoria: ELITE FOUR")
        randomize_bosses = "RANDOMIZE_BOSS_TRAINERS TRUE" in content
        details.append(f"RANDOMIZE_BOSS_TRAINERS: {randomize_bosses}")
        if not randomize_bosses:
            details.append("BLOQUEADO: Boss trainers desabilitados")
    elif trainer_id in giovanni_ids:
        details.append("Categoria: GIOVANNI")
        randomize_bosses = "RANDOMIZE_BOSS_TRAINERS TRUE" in content
        details.append(f"RANDOMIZE_BOSS_TRAINERS: {randomize_bosses}")
        if not randomize_bosses:
            details.append("BLOQUEADO: Boss trainers desabilitados")
    elif trainer_id in rival_ids:
        details.append("Categoria: RIVAL")
        randomize_important = "RANDOMIZE_IMPORTANT_TRAINERS TRUE" in content
        details.append(f"RANDOMIZE_IMPORTANT_TRAINERS: {randomize_important}")
        if not randomize_important:
            details.append("BLOQUEADO: Important trainers desabilitados")
    else:
        details.append("Categoria: REGULAR")
        randomize_regular = "RANDOMIZE_REGULAR_TRAINERS TRUE" in content
        details.append(f"RANDOMIZE_REGULAR_TRAINERS: {randomize_regular}")
        if not randomize_regular:
            details.append("BLOQUEADO: Regular trainers desabilitados")
    
    return details

def TestSpecificTrainerIDs():
    """Testa IDs específicos para verificar conversão"""
    
    print("\n🔢 TESTE DE IDs ESPECÍFICOS:")
    print("=" * 30)
    
    # Converter IDs conhecidos
    known_trainers = {
        0x19E: "Brock (414)",
        0x19F: "Misty (415)", 
        0x1A0: "Lt. Surge (416)",
        0x1A1: "Erika (417)",
        0x1A2: "Koga (418)",
        0x1A4: "Sabrina (420)",
        0x1A3: "Blaine (419)",
        0x15E: "Giovanni (350)"
    }
    
    for hex_id, name in known_trainers.items():
        decimal_id = hex_id
        print(f"   0x{hex_id:03X} = {decimal_id:3d} ({name})")

def main():
    """Função principal"""
    
    TestTrainerEligibility()
    TestSpecificTrainerIDs()
    
    print("\n" + "=" * 50)
    print("💡 DIAGNÓSTICO:")
    print("   Se todos os trainers aparecem como 'NÃO ELEGÍVEL',")
    print("   o problema está na configuração ou na função de elegibilidade.")
    print("   Se alguns aparecem como 'ELEGÍVEL', o problema está")
    print("   na função de leitura de dados da ROM.")

if __name__ == "__main__":
    main()
