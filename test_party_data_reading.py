#!/usr/bin/env python3
"""
Teste de Leitura de Party Data
Verifica se ReadOriginalTrainerPartyData está funcionando corretamente
"""

import struct
import os

def GetPokemonDataSize(party_flags):
    """Determina o tamanho correto dos dados de Pokemon baseado nos partyFlags"""
    if party_flags & 0x01:  # Custom moves (bit 0)
        return 16  # Tipo 1 ou 3 - 16 bytes por Pokemon
    else:
        return 8   # Tipo 0 ou 2 - 8 bytes por Pokemon

def ReadPokemonDataCorrect(rom, party_offset, party_flags, pokemon_index):
    """Lê dados de um Pokemon específico usando a estrutura correta"""
    entry_size = GetPokemonDataSize(party_flags)
    pokemon_offset = party_offset + (pokemon_index * entry_size)
    
    rom.seek(pokemon_offset)
    data = rom.read(entry_size)
    
    if len(data) < entry_size:
        return None
    
    # Parse básico (comum a todas as estruturas)
    iv = data[0]
    level = data[2]  # Level sempre no offset 2
    species = struct.unpack('<H', data[4:6])[0]
    
    return {
        'iv': iv,
        'level': level,
        'species': species,
        'entry_size': entry_size
    }

def ReadOriginalTrainerPartyData(original_rom, party_offset, party_size, party_flags=None):
    """STEP 1: Read trainer party data from ROM ONCE using CORRECT structure detection"""
    import struct

    try:
        # Se party_flags não foi fornecido, tentar detectar automaticamente
        if party_flags is None:
            # Ler primeiro Pokemon com estrutura 8 bytes para detectar padrão
            original_rom.seek(party_offset)
            test_data = original_rom.read(16)  # Ler 16 bytes para análise
            
            if len(test_data) >= 8:
                level_8 = test_data[2]
                species_8 = struct.unpack('<H', test_data[4:6])[0]
                
                # Se level ou species são inválidos com 8 bytes, provavelmente é 16 bytes
                if level_8 > 100 or species_8 > 1440:
                    party_flags = 0x01  # Assumir custom moves (16 bytes)
                else:
                    party_flags = 0x00  # Assumir estrutura básica (8 bytes)
            else:
                party_flags = 0x00  # Default para 8 bytes

        entry_size = GetPokemonDataSize(party_flags)
        
        original_party = []
        for i in range(party_size):
            pokemon_data = ReadPokemonDataCorrect(original_rom, party_offset, party_flags, i)
            
            if pokemon_data and pokemon_data['level'] > 0 and pokemon_data['level'] <= 255 and pokemon_data['species'] > 0 and pokemon_data['species'] <= 1440:
                original_party.append({
                    'slot': i,
                    'level': pokemon_data['level'],
                    'species': pokemon_data['species'],
                    'iv': pokemon_data['iv'],
                    'entry_size': entry_size
                })

        return original_party

    except Exception as e:
        print(f"❌ Erro em ReadOriginalTrainerPartyData: {e}")
        return None

def TestPartyDataReading():
    """Testa a leitura de party data para trainers específicos"""
    
    print("🔍 TESTE DE LEITURA DE PARTY DATA")
    print("=" * 50)
    
    rom_path = "BPRE0.gba"
    
    if not os.path.exists(rom_path):
        print(f"❌ ROM não encontrada: {rom_path}")
        return
    
    # Fire Red offsets
    TRAINER_DATA_OFFSET = 0x23EAC8
    TRAINER_ENTRY_SIZE = 40
    
    test_trainers = [1, 2, 3, 418, 419, 420]  # Alguns regulares + gym leaders
    
    with open(rom_path, 'rb') as rom:
        for trainer_id in test_trainers:
            print(f"\n🎯 Testando Trainer {trainer_id}:")
            
            # Ler dados básicos do trainer
            trainer_offset = TRAINER_DATA_OFFSET + (trainer_id * TRAINER_ENTRY_SIZE)
            rom.seek(trainer_offset)
            trainer_data = rom.read(40)
            
            if len(trainer_data) < 40:
                print(f"   ❌ Dados insuficientes")
                continue
            
            party_flags = trainer_data[0]
            trainer_class = trainer_data[1]
            party_size = trainer_data[32]
            party_ptr = struct.unpack('<I', trainer_data[36:40])[0]
            
            print(f"   📊 Party Flags: 0x{party_flags:02X}")
            print(f"   📊 Trainer Class: {trainer_class}")
            print(f"   📊 Party Size: {party_size}")
            print(f"   📊 Party Ptr: 0x{party_ptr:08X}")
            
            # Verificar ponteiro
            if party_ptr == 0 or party_ptr < 0x08000000 or party_ptr >= 0x09000000:
                print(f"   ❌ Ponteiro inválido")
                continue
            
            party_offset = party_ptr - 0x08000000
            print(f"   📊 Party Offset: 0x{party_offset:08X}")
            
            # Verificar party size
            if party_size <= 0 or party_size > 6:
                print(f"   ❌ Party size inválido")
                continue
            
            # Testar ReadOriginalTrainerPartyData
            party_data = ReadOriginalTrainerPartyData(rom, party_offset, party_size, party_flags)
            
            if party_data is None:
                print(f"   ❌ ReadOriginalTrainerPartyData retornou None")
                continue
            
            if len(party_data) == 0:
                print(f"   ❌ ReadOriginalTrainerPartyData retornou lista vazia")
                continue
            
            print(f"   ✅ ReadOriginalTrainerPartyData funcionou!")
            print(f"   📊 Pokemon lidos: {len(party_data)}")
            
            for i, pokemon in enumerate(party_data):
                print(f"      Slot {i+1}: Species {pokemon['species']:3d}, Level {pokemon['level']:3d}, IV {pokemon['iv']:3d}")
    
    return True

def TestFullTrainerReading():
    """Testa a leitura completa como feita no sistema"""
    
    print(f"\n🔍 TESTE DE LEITURA COMPLETA")
    print("=" * 50)
    
    rom_path = "BPRE0.gba"
    
    if not os.path.exists(rom_path):
        print(f"❌ ROM não encontrada: {rom_path}")
        return
    
    # Fire Red offsets
    TRAINER_DATA_OFFSET = 0x23EAC8
    TRAINER_COUNT = 0x2E7
    TRAINER_ENTRY_SIZE = 40
    
    valid_trainers = 0
    invalid_trainers = 0
    eligible_trainers = 0
    
    with open(rom_path, 'rb') as rom:
        for trainer_id in range(1, min(50, TRAINER_COUNT)):  # Testar apenas os primeiros 50
            trainer_offset = TRAINER_DATA_OFFSET + (trainer_id * TRAINER_ENTRY_SIZE)
            
            # Ler dados básicos do trainer
            rom.seek(trainer_offset)
            trainer_data = rom.read(40)
            
            if len(trainer_data) < 40:
                invalid_trainers += 1
                continue
            
            party_flags = trainer_data[0]
            trainer_class = trainer_data[1]
            party_size = trainer_data[32]
            party_ptr = struct.unpack('<I', trainer_data[36:40])[0]
            
            # Verificar ponteiro
            if party_ptr == 0 or party_ptr < 0x08000000 or party_ptr >= 0x09000000:
                invalid_trainers += 1
                continue
            
            # Verificar party size
            if party_size <= 0 or party_size > 6:
                invalid_trainers += 1
                continue
            
            party_offset = party_ptr - 0x08000000
            
            # Testar ReadOriginalTrainerPartyData
            party_data = ReadOriginalTrainerPartyData(rom, party_offset, party_size, party_flags)
            
            if party_data is None or len(party_data) == 0:
                invalid_trainers += 1
                continue
            
            # Verificar elegibilidade (simplificado)
            is_eligible = IsTrainerEligibleSimple(trainer_class, trainer_id)
            
            valid_trainers += 1
            if is_eligible:
                eligible_trainers += 1
    
    print(f"📊 RESULTADOS:")
    print(f"   ✅ Trainers válidos: {valid_trainers}")
    print(f"   ❌ Trainers inválidos: {invalid_trainers}")
    print(f"   🎯 Trainers elegíveis: {eligible_trainers}")
    
    if eligible_trainers == 0:
        print(f"\n⚠️  PROBLEMA: Nenhum trainer elegível encontrado!")
        print(f"   Isso explica por que o sistema está lendo 0 trainers.")
    else:
        print(f"\n✅ Sistema deveria funcionar com {eligible_trainers} trainers elegíveis")

def IsTrainerEligibleSimple(trainer_class, trainer_id):
    """Versão simplificada da verificação de elegibilidade"""
    
    # Gym Leaders
    gym_leader_ids = [0x19E, 0x19F, 0x1A0, 0x1A1, 0x1A2, 0x1A4, 0x1A3, 0x15E]
    
    # Elite Four
    elite_four_ids = [0x19A, 0x19B, 0x19C, 0x19D, 0x2DF, 0x2E0, 0x2E1, 0x2E2]
    
    # Giovanni
    giovanni_ids = [0x15C, 0x15D]
    
    # Rivals
    rival_ids = [
        0x148, 0x146, 0x147, 0x14B, 0x149, 0x14A, 0x14E, 0x14C, 0x14D,
        0x1A9, 0x1A7, 0x1A8, 0x1AF, 0x1AD, 0x1AE, 0x1B2, 0x1B0, 0x1B1,
        0x1B5, 0x1B3, 0x1B4, 0x1B8, 0x1B6, 0x1B7, 0x2E5, 0x2E3, 0x2E4
    ]
    
    # Para este teste, assumir que todas as categorias estão habilitadas
    # (como visto no teste anterior)
    return True

def main():
    """Função principal"""
    
    TestPartyDataReading()
    TestFullTrainerReading()
    
    print("\n" + "=" * 50)
    print("💡 CONCLUSÃO:")
    print("   Se ReadOriginalTrainerPartyData está funcionando mas")
    print("   o sistema ainda lê 0 trainers, o problema pode estar:")
    print("   1. Na função IsValidPointer")
    print("   2. Na função de análise de tipos")
    print("   3. Em alguma exceção não capturada")

if __name__ == "__main__":
    main()
