#!/usr/bin/env python3
"""
Debug da Leitura de Trainers
Investiga por que 0 trainers estão sendo lidos
"""

import struct
import os

def DebugTrainerReading():
    """Debug da leitura de trainers"""
    
    print("🔍 DEBUG DA LEITURA DE TRAINERS")
    print("=" * 50)
    
    rom_path = "BPRE0.gba"
    
    if not os.path.exists(rom_path):
        print(f"❌ ROM não encontrada: {rom_path}")
        return
    
    # Fire Red offsets
    TRAINER_DATA_OFFSET = 0x23EAC8
    TRAINER_COUNT = 0x2E7  # 743 trainers
    TRAINER_ENTRY_SIZE = 40
    
    print(f"📊 Analisando {TRAINER_COUNT} trainers...")
    print(f"📊 Offset base: 0x{TRAINER_DATA_OFFSET:08X}")
    
    valid_trainers = 0
    invalid_trainers = 0
    valid_pointers = 0
    invalid_pointers = 0
    
    with open(rom_path, 'rb') as rom:
        rom.seek(0, 2)
        rom_size = rom.tell()
        print(f"📊 Tamanho da ROM: {rom_size:,} bytes")
        
        for trainer_id in range(1, min(20, TRAINER_COUNT)):  # Testar apenas os primeiros 20
            trainer_offset = TRAINER_DATA_OFFSET + (trainer_id * TRAINER_ENTRY_SIZE)
            
            rom.seek(trainer_offset)
            trainer_data = rom.read(40)
            
            if len(trainer_data) < 40:
                print(f"❌ Trainer {trainer_id}: Dados insuficientes ({len(trainer_data)} bytes)")
                invalid_trainers += 1
                continue
            
            party_flags = trainer_data[0]
            trainer_class = trainer_data[1]
            party_size = trainer_data[32]
            party_ptr = struct.unpack('<I', trainer_data[36:40])[0]
            
            print(f"\n🎯 Trainer {trainer_id}:")
            print(f"   Party Flags: 0x{party_flags:02X}")
            print(f"   Trainer Class: {trainer_class}")
            print(f"   Party Size: {party_size}")
            print(f"   Party Ptr: 0x{party_ptr:08X}")
            
            # Verificar ponteiro
            is_valid_ptr = party_ptr != 0 and 0x08000000 <= party_ptr < 0x09000000
            print(f"   Ponteiro válido: {'✅' if is_valid_ptr else '❌'}")
            
            if is_valid_ptr:
                valid_pointers += 1
                party_offset = party_ptr - 0x08000000
                print(f"   Party Offset: 0x{party_offset:08X}")
                
                # Verificar se offset está dentro da ROM
                if party_offset < rom_size:
                    print(f"   Offset dentro da ROM: ✅")
                    
                    # Verificar party size
                    if 0 < party_size <= 6:
                        print(f"   Party size válido: ✅")
                        
                        # Tentar ler dados da party
                        rom.seek(party_offset)
                        party_data = rom.read(party_size * 16)  # Ler como 16 bytes para teste
                        
                        if len(party_data) >= party_size * 8:
                            print(f"   Dados da party legíveis: ✅")
                            
                            # Analisar primeiro Pokemon
                            if len(party_data) >= 8:
                                level = party_data[2]
                                species = struct.unpack('<H', party_data[4:6])[0]
                                print(f"   Primeiro Pokemon: Species {species}, Level {level}")
                                
                                if level > 0 and level <= 255 and species > 0 and species <= 1440:
                                    print(f"   Dados do Pokemon válidos: ✅")
                                    valid_trainers += 1
                                else:
                                    print(f"   Dados do Pokemon inválidos: ❌")
                                    invalid_trainers += 1
                            else:
                                print(f"   Dados insuficientes para Pokemon: ❌")
                                invalid_trainers += 1
                        else:
                            print(f"   Não foi possível ler dados da party: ❌")
                            invalid_trainers += 1
                    else:
                        print(f"   Party size inválido: ❌")
                        invalid_trainers += 1
                else:
                    print(f"   Offset fora da ROM: ❌")
                    invalid_trainers += 1
            else:
                invalid_pointers += 1
                invalid_trainers += 1
    
    print(f"\n" + "=" * 50)
    print(f"📊 RESUMO:")
    print(f"   ✅ Trainers válidos: {valid_trainers}")
    print(f"   ❌ Trainers inválidos: {invalid_trainers}")
    print(f"   ✅ Ponteiros válidos: {valid_pointers}")
    print(f"   ❌ Ponteiros inválidos: {invalid_pointers}")
    
    if valid_trainers == 0:
        print(f"\n⚠️  PROBLEMA IDENTIFICADO:")
        print(f"   Nenhum trainer válido encontrado!")
        print(f"   Possíveis causas:")
        print(f"   1. Offset da tabela de trainers incorreto")
        print(f"   2. Estrutura de dados incorreta")
        print(f"   3. ROM corrompida ou versão incorreta")
    
    return valid_trainers > 0

def TestSpecificTrainer(trainer_id):
    """Testa um trainer específico em detalhes"""
    
    print(f"\n🎯 TESTE DETALHADO - Trainer {trainer_id}")
    print("=" * 40)
    
    rom_path = "BPRE0.gba"
    TRAINER_DATA_OFFSET = 0x23EAC8
    TRAINER_ENTRY_SIZE = 40
    
    with open(rom_path, 'rb') as rom:
        trainer_offset = TRAINER_DATA_OFFSET + (trainer_id * TRAINER_ENTRY_SIZE)
        
        print(f"📊 Trainer Offset: 0x{trainer_offset:08X}")
        
        rom.seek(trainer_offset)
        trainer_data = rom.read(40)
        
        print(f"📊 Dados lidos: {len(trainer_data)} bytes")
        print(f"📊 Hex dump: {trainer_data[:20].hex()}")
        
        if len(trainer_data) >= 40:
            party_flags = trainer_data[0]
            trainer_class = trainer_data[1]
            party_size = trainer_data[32]
            party_ptr = struct.unpack('<I', trainer_data[36:40])[0]
            
            print(f"📊 Party Flags: 0x{party_flags:02X}")
            print(f"📊 Trainer Class: {trainer_class}")
            print(f"📊 Party Size: {party_size}")
            print(f"📊 Party Ptr: 0x{party_ptr:08X}")
            
            if party_ptr != 0 and 0x08000000 <= party_ptr < 0x09000000:
                party_offset = party_ptr - 0x08000000
                print(f"📊 Party Offset: 0x{party_offset:08X}")
                
                # Ler dados da party
                rom.seek(party_offset)
                party_data = rom.read(party_size * 16)
                
                print(f"📊 Party data: {len(party_data)} bytes")
                print(f"📊 Party hex: {party_data[:32].hex()}")
                
                # Analisar cada Pokemon
                for i in range(party_size):
                    offset_8 = i * 8
                    offset_16 = i * 16
                    
                    if offset_8 + 8 <= len(party_data):
                        level_8 = party_data[offset_8 + 2]
                        species_8 = struct.unpack('<H', party_data[offset_8 + 4:offset_8 + 6])[0]
                        print(f"   Pokemon {i+1} (8 bytes): Species {species_8}, Level {level_8}")
                    
                    if offset_16 + 16 <= len(party_data):
                        level_16 = party_data[offset_16 + 2]
                        species_16 = struct.unpack('<H', party_data[offset_16 + 4:offset_16 + 6])[0]
                        print(f"   Pokemon {i+1} (16 bytes): Species {species_16}, Level {level_16}")

def main():
    """Função principal"""
    
    success = DebugTrainerReading()
    
    if success:
        print(f"\n✅ Alguns trainers válidos encontrados!")
        print(f"   O problema pode estar na função IsTrainerEligibleForRandomization")
    else:
        print(f"\n❌ Nenhum trainer válido encontrado!")
        print(f"   O problema está na leitura básica dos dados")
    
    # Testar trainers específicos conhecidos
    test_trainers = [418, 419, 420]  # Koga, Sabrina, Blaine
    
    for trainer_id in test_trainers:
        TestSpecificTrainer(trainer_id)

if __name__ == "__main__":
    main()
