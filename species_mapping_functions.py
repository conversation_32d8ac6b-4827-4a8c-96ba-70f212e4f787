
def MapRomSpeciesToProjectSpecies(rom_species_id):
    """
    Mapeia ROM Species ID para Projeto Species ID baseado em nomes
    ROM Species ID ≠ Projeto Species ID devido à expansão de Pokémon
    """
    rom_to_project_mapping = {
    }
    
    return rom_to_project_mapping.get(rom_species_id, rom_species_id)

def MapProjectSpeciesToRomSpecies(project_species_id):
    """
    Mapeia Projeto Species ID para ROM Species ID (reverso)
    """
    project_to_rom_mapping = {
    }
    
    return project_to_rom_mapping.get(project_species_id, project_species_id)
