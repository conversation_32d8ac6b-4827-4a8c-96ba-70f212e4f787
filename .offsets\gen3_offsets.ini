[Ruby (U)]
Game=AXVE
Version=0
Type=Ruby
TableFile=gba_english
FreeSpace=0x700000
PokemonNameLength=11
PokemonCount=411
PokemonStats=0x1FEC18
PokemonMovesets=0x207BC8
EggMoves=0x2091DC
PokemonTMHMCompat=0x1FD0F0
PokemonEvolutions=0x203B68
StarterPokemon=0x3F76C4
StarterItems=0x821AA
TrainerData=0x1F04FC
TrainerEntrySize=40
TrainerCount=0x2B6
TrainerClassNames=0x1F0208
TrainerClassCount=58
TrainerClassNameLength=13
TrainerNameLength=12
DoublesTrainerClasses=[27, 42, 55, 56, 57]
EliteFourIndices=[261, 262, 263, 264, 335]
ItemData=0x3C5564
ItemCount=348
ItemEntrySize=44
MoveData=0x1FB12C
MoveCount=354
MoveDescriptions=0x3C09D8
MoveNameLength=13
MoveNames=0x1F8320
AbilityNameLength=13
AbilityNames=0x1FA248
TmMoves=0x376504
IntroCryOffset=0xA506
IntroSpriteOffset=0xB2B8
IntroPaletteOffset=0xB2C4
IntroOtherOffset=0xB286
PokemonFrontSprites=0x1E8354
PokemonNormalPalettes=0x1EA5B4
ItemBallPic=59
TradeTableOffset=0x215AC4
TradeTableSize=3
TradesUnused=[]
RunIndoorsTweakOffset=0xE5E00
CatchingTutorialOpponentMonOffset=0x81B00
CatchingTutorialPlayerMonOffset=0x10F62E
PCPotionOffset=0x4062F0
PickupTableStartLocator=16001E00170028000200320044003C
PickupItemCount=10
InstantTextTweak=instant_text/ruby_10_instant_text
TypeEffectivenessOffset=0x1F9720
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x157663, 0x157691], Level=[0x157693]} // Lileep
StaticPokemon{}={Species=[0x1576B6, 0x1576E4], Level=[0x1576E6]} // Anorith
StaticPokemon{}={Species=[0x1A04A3, 0x15DE26, 0x15DE2D], Level=[0x15DE28]} // Groudon
StaticPokemon{}={Species=[0x15CB89, 0x15CB92], Level=[0x15CB94]} // Regirock
StaticPokemon{}={Species=[0x15EFA1, 0x15EFAA], Level=[0x15EFAC]} // Regice
StaticPokemon{}={Species=[0x15F054, 0x15F05D], Level=[0x15F05F]} // Registeel
StaticPokemon{}={Species=[0x160BCE, 0x160BF4], Level=[0x160BF6]} // Latias (Southern Island)
StaticPokemon{}={Species=[0x15F319, 0x15F320], Level=[0x15F31B]} // Rayquaza
StaticPokemon{}={Species=[0x1A05E2, 0x1A05EB], Level=[0x1A05ED]} // Kecleons on OW (7)
StaticPokemon{}={Species=[0x1518E8, 0x1518F1], Level=[0x1518F3]} // Kecleon w/ Steven
StaticPokemon{}={Species=[0x15E903, 0x15E90A], Level=[0x15E905]} // Voltorb 1
StaticPokemon{}={Species=[0x15E921, 0x15E928], Level=[0x15E923]} // Voltorb 2
StaticPokemon{}={Species=[0x15E93F, 0x15E946], Level=[0x15E941]} // Voltorb 3
StaticPokemon{}={Species=[0x1A0500, 0x1A0507], Level=[0x1A0502]} // Electrode 1
StaticPokemon{}={Species=[0x1A051E, 0x1A0525], Level=[0x1A0520]} // Electrode 2
StaticPokemon{}={Species=[0x14E79A]} // Wynaut Egg
StaticPokemon{}={Species=[0x15AAAF, 0x15AABF], Level=[0x15AAB1]} // Beldum
StaticPokemon{}={Species=[0x163D99], Level=[0x163D9B]} // Castform
RoamingPokemon{}={Species=[0x110988, 0x1342F8], Level=[0x13425C, 0x13426C]} // Latios
StaticEggPokemonOffsets=[15]
StaticFirstBattleTweak=hardcoded_statics/rs_firstbattle
StaticFirstBattleSpeciesOffset=0xFE003C
StaticFirstBattleLevelOffset=0xFE0008
StaticFirstBattleOffset=18
FindMapsWithMonFunctionStartOffset=0x110908
CreateInitialRoamerMonFunctionStartOffset=0x134240
TMText[]=[3,15,0,1,0x70,The TM I handed you contains [move].]
TMText[]=[4,14,0,1,0x74,TATE: That TM04 contains... LIZA: [move]!\pTATE: It’s a move that’s perfect... LIZA: For any POKéMON!]
TMText[]=[5,0,29,12,0x0D,All my POKéMON does is [move]... No one dares to come near me...\pSigh... If you would, please take this TM away...]
TMText[]=[5,0,29,12,0x2F,TM05 contains [move].]
TMText[]=[8,3,3,1,0x7C,That TM08 contains [move].]
TMText[]=[9,0,19,32,0x0D,I like filling my mouth with seeds, then spitting them out fast!\pI like you, so you can have this!\pUse it on a POKéMON, and it will learn [move].\pWhat does that have to do with firing seeds? Well, nothing!]
TMText[]=[24,0,2,8,0x4C,WATTSON: Wahahahaha!\pI knew it, \v01\v05! I knew I’d made the right choice asking you!\pThis is my thanks - a TM containing [move]!\pGo on, you’ve earned it!]
TMText[]=[31,15,5,1,0x24,TM31 contains [move]! It’s a move so horrible that I can’t describe it.]
TMText[]=[34,10,0,1,0x8B,That TM34 there contains [move]. You can count on it!]
TMText[]=[39,11,3,1,0x7F,That TM39 contains [move].\pIf you use a TM, it instantly teaches the move to a POKéMON.\pRemember, a TM can be used only once, so think before you use it.]
TMText[]=[40,12,1,1,0x67,TM40 contains [move].]
TMText[]=[41,9,3,13,0x2F,That’s, like, TM41, you know? Hey, it’s [move], you hearing me?\pHey, now, you listen here, like, I’m not laying a torment on you!]
TMText[]=[42,8,1,1,0x48F,DAD: TM42 contains [move].\pIt might be able to turn a bad situation into an advantage.]
TMText[]=[47,24,10,1,0x19,STEVEN: Okay, thank you.\pYou went through all this trouble to deliver that. I need to thank you.\pLet me see... I’ll give you this TM.\pIt contains my favorite move, [move].]
TMText[]=[50,4,1,1,0x7F,That TM50 contains [move].]
MainGameLegendaries=[383]
ShopItemOffsets=[0x14BAD0, 0x14BEB4, 0x152F9C, 0x152FB8, 0x153640, 0x1538E4, 0x153980, 0x153ED4, 0x1552D0, 0x1552FC, 0x156428, 0x1573D8, 0x157C00, 0x157C28, 0x158080, 0x159F44, 0x159F78, 0x159FA8, 0x159FD0, 0x15A030, 0x15A054, 0x15A940, 0x15B234, 0x15BAC0]
SkipShops=[1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23]
MainGameShops=[0, 4, 17, 18]
CRC32=F0815EE7

[Ruby (E)]
Game=AXVE
Version=1
Type=Ruby
CopyTMText=1
CopyFrom=Ruby (U)
PokemonStats=0x1FEC30
PokemonMovesets=0x207BE0
EggMoves=0x2091F4
PokemonTMHMCompat=0x1FD108
PokemonEvolutions=0x203B80
StarterPokemon=0x3F76E0
StarterItems=0x821CA
TrainerData=0x1F0514
TrainerClassNames=0x1F0220
ItemData=0x3C5580
MoveData=0x1FB144
MoveDescriptions=0x3C09F4
MoveNames=0x1F8338
AbilityNames=0x1FA260
TmMoves=0x37651C
PokemonFrontSprites=0x1E836C
PokemonNormalPalettes=0x1EA5CC
TradeTableOffset=0x215ADC
RunIndoorsTweakOffset=0xE5E20
CatchingTutorialOpponentMonOffset=0x81B20
CatchingTutorialPlayerMonOffset=0x10F64E
PCPotionOffset=0x40630C
InstantTextTweak=instant_text/ruby_11_instant_text
TypeEffectivenessOffset=0x1F9738
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x157683, 0x1576B1], Level=[0x1576B3]} // Lileep
StaticPokemon{}={Species=[0x1576D6, 0x157704], Level=[0x157706]} // Anorith
StaticPokemon{}={Species=[0x1A04C3, 0x15DE46, 0x15DE4D], Level=[0x15DE48]} // Groudon
StaticPokemon{}={Species=[0x15CBA9, 0x15CBB2], Level=[0x15CBB4]} // Regirock
StaticPokemon{}={Species=[0x15EFC1, 0x15EFCA], Level=[0x15EFCC]} // Regice
StaticPokemon{}={Species=[0x15F074, 0x15F07D], Level=[0x15F07F]} // Registeel
StaticPokemon{}={Species=[0x160BEE, 0x160C14], Level=[0x160C16]} // Latias (Southern Island)
StaticPokemon{}={Species=[0x15F339, 0x15F340], Level=[0x15F33B]} // Rayquaza
StaticPokemon{}={Species=[0x1A0602, 0x1A060B], Level=[0x1A060D]} // Kecleons on OW (7)
StaticPokemon{}={Species=[0x151908, 0x151911], Level=[0x151913]} // Kecleon w/ Steven
StaticPokemon{}={Species=[0x15E923, 0x15E92A], Level=[0x15E925]} // Voltorb 1
StaticPokemon{}={Species=[0x15E941, 0x15E948], Level=[0x15E943]} // Voltorb 2
StaticPokemon{}={Species=[0x15E95F, 0x15E966], Level=[0x15E961]} // Voltorb 3
StaticPokemon{}={Species=[0x1A0520, 0x1A0527], Level=[0x1A0522]} // Electrode 1
StaticPokemon{}={Species=[0x1A053E, 0x1A0545], Level=[0x1A0540]} // Electrode 2
StaticPokemon{}={Species=[0x14E7BA]} // Wynaut Egg
StaticPokemon{}={Species=[0x15AACF, 0x15AADF], Level=[0x15AAD1]} // Beldum
StaticPokemon{}={Species=[0x163DB9], Level=[0x163DBB]} // Castform
RoamingPokemon{}={Species=[0x1109A8, 0x134318], Level=[0x13427C, 0x13428C]} // Latios
StaticFirstBattleTweak=hardcoded_statics/rs_firstbattle
FindMapsWithMonFunctionStartOffset=0x110928
CreateInitialRoamerMonFunctionStartOffset=0x134260
ShopItemOffsets=[0x14BAF0, 0x14BED4, 0x152FBC, 0x152FD8, 0x153660, 0x153904, 0x1539A0, 0x153EF4, 0x1552F0, 0x15531C, 0x156448, 0x1573F8, 0x157C20, 0x157C48, 0x1580A0, 0x159F64, 0x159F98, 0x159FC8, 0x159FF0, 0x15A050, 0x15A074, 0x15A960, 0x15B254, 0x15BAE0]
CRC32=61641576

[Ruby (U/E) 1.2]
Game=AXVE
Version=2
Type=Ruby
CopyTMText=1
CopyStaticPokemon=1
CopyFrom=Ruby (E)
InstantTextTweak=instant_text/ruby_12_instant_text
StaticFirstBattleTweak=hardcoded_statics/rs_firstbattle
CRC32=AEAC73E6

[Sapphire (U)]
Game=AXPE
Version=0
Type=Sapp
CopyTMText=1
CopyFrom=Ruby (U)
PokemonStats=0x1FEBA8
PokemonMovesets=0x207B58
EggMoves=0x20916C
PokemonTMHMCompat=0x1FD080
PokemonEvolutions=0x203AF8
StarterPokemon=0x3F771C
TrainerData=0x1F048C
TrainerClassNames=0x1F0198
ItemData=0x3C55BC
MoveData=0x1FB0BC
MoveDescriptions=0x3C0A30
MoveNames=0x1F82B0
AbilityNames=0x1FA1D8
TmMoves=0x376494
PokemonFrontSprites=0x1E82E4
PokemonNormalPalettes=0x1EA544
TradeTableOffset=0x215A54
RunIndoorsTweakOffset=0xE5E00
PCPotionOffset=0x406348
InstantTextTweak=instant_text/sapphire_10_instant_text
TypeEffectivenessOffset=0x1F96B0
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x1575F3, 0x157621], Level=[0x157623]} // Lileep
StaticPokemon{}={Species=[0x157646, 0x157674], Level=[0x157676]} // Anorith
StaticPokemon{}={Species=[0x1A0433, 0x15DDB6, 0x15DDBD], Level=[0x15DDB8]} // Kyogre
StaticPokemon{}={Species=[0x15CB19, 0x15CB22], Level=[0x15CB24]} // Regirock
StaticPokemon{}={Species=[0x15EF31, 0x15EF3A], Level=[0x15EF3C]} // Regice
StaticPokemon{}={Species=[0x15EFE4, 0x15EFED], Level=[0x15EFEF]} // Registeel
StaticPokemon{}={Species=[0x160B5E, 0x160B84], Level=[0x160B86]} // Latios (Southern Island)
StaticPokemon{}={Species=[0x15F2A9, 0x15F2B0], Level=[0x15F2AB]} // Rayquaza
StaticPokemon{}={Species=[0x1A0572, 0x1A057B], Level=[0x1A057D]} // Kecleons on OW (7)
StaticPokemon{}={Species=[0x15187C, 0x151885], Level=[0x151887]} // Kecleon w/ Steven
StaticPokemon{}={Species=[0x15E893, 0x15E89A], Level=[0x15E895]} // Voltorb 1
StaticPokemon{}={Species=[0x15E8B1, 0x15E8B8], Level=[0x15E8B3]} // Voltorb 2
StaticPokemon{}={Species=[0x15E8CF, 0x15E8D6], Level=[0x15E8D1]} // Voltorb 3
StaticPokemon{}={Species=[0x1A0490, 0x1A0497], Level=[0x1A0492]} // Electrode 1
StaticPokemon{}={Species=[0x1A04AE, 0x1A04B5], Level=[0x1A04B0]} // Electrode 2
StaticPokemon{}={Species=[0x14E72E]} // Wynaut Egg
StaticPokemon{}={Species=[0x15AA3F, 0x15AA4F], Level=[0x15AA41]} // Beldum
StaticPokemon{}={Species=[0x163D29], Level=[0x163D2B]} // Castform
RoamingPokemon{}={Species=[0x110984, 0x1342FC], Level=[0x13425A, 0x13426A]} // Latias
MainGameLegendaries=[382]
StaticFirstBattleTweak=hardcoded_statics/rs_firstbattle
ShopItemOffsets=[0x14BAD0, 0x14BEB4, 0x152F2C, 0x152F48, 0x1535D0, 0x153874, 0x153910, 0x153E64, 0x155260, 0x15528C, 0x1563B8, 0x157368, 0x157B90, 0x157BB8, 0x158010, 0x159ED4, 0x159F08, 0x159F38, 0x159F60, 0x159FC0, 0x159FE4, 0x15A8D0, 0x15B1C4, 0x15BA50]
CRC32=554DEDC4

[Sapphire (E)]
Game=AXPE
Version=1
Type=Sapp
CopyTMText=1
CopyFrom=Sapphire (U)
PokemonStats=0x1FEBC0
PokemonMovesets=0x207B70
EggMoves=0x209184
PokemonTMHMCompat=0x1FD098
PokemonEvolutions=0x203B10
StarterPokemon=0x3F773C
StarterItems=0x821CA
TrainerData=0x1F04A4
TrainerClassNames=0x1F01B0
ItemData=0x3C55DC
MoveData=0x1FB0D4
MoveDescriptions=0x3C0A50
MoveNames=0x1F82C8
AbilityNames=0x1FA1F0
TmMoves=0x3764AC
PokemonFrontSprites=0x1E82FC
PokemonNormalPalettes=0x1EA55C
TradeTableOffset=0x215A6C
RunIndoorsTweakOffset=0xE5E20
CatchingTutorialOpponentMonOffset=0x81B20
CatchingTutorialPlayerMonOffset=0x10F64E
PCPotionOffset=0x406368
InstantTextTweak=instant_text/sapphire_11_instant_text
TypeEffectivenessOffset=0x1F96C8
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x157613, 0x157641], Level=[0x157643]} // Lileep
StaticPokemon{}={Species=[0x157666, 0x157694], Level=[0x157696]} // Anorith
StaticPokemon{}={Species=[0x1A0453, 0x15DDD6, 0x15DDDD], Level=[0x15DDD8]} // Kyogre
StaticPokemon{}={Species=[0x15CB39, 0x15CB42], Level=[0x15CB44]} // Regirock
StaticPokemon{}={Species=[0x15EF51, 0x15EF5A], Level=[0x15EF5C]} // Regice
StaticPokemon{}={Species=[0x15F004, 0x15F00D], Level=[0x15F00F]} // Registeel
StaticPokemon{}={Species=[0x160B7E, 0x160BA4], Level=[0x160BA6]} // Latios (Southern Island)
StaticPokemon{}={Species=[0x15F2C9, 0x15F2D0], Level=[0x15F2CB]} // Rayquaza
StaticPokemon{}={Species=[0x1A0592, 0x1A059B], Level=[0x1A059D]} // Kecleons on OW (7)
StaticPokemon{}={Species=[0x15189C, 0x1518A5], Level=[0x1518A7]} // Kecleon w/ Steven
StaticPokemon{}={Species=[0x15E8B3, 0x15E8BA], Level=[0x15E8B5]} // Voltorb 1
StaticPokemon{}={Species=[0x15E8D1, 0x15E8D8], Level=[0x15E8D3]} // Voltorb 2
StaticPokemon{}={Species=[0x15E8EF, 0x15E8F6], Level=[0x15E8F1]} // Voltorb 3
StaticPokemon{}={Species=[0x1A04B0, 0x1A04B7], Level=[0x1A04B2]} // Electrode 1
StaticPokemon{}={Species=[0x1A04CE, 0x1A04D5], Level=[0x1A04D0]} // Electrode 2
StaticPokemon{}={Species=[0x14E74E]} // Wynaut Egg
StaticPokemon{}={Species=[0x15AA5F, 0x15AA6F], Level=[0x15AA61]} // Beldum
StaticPokemon{}={Species=[0x163D49], Level=[0x163D4B]} // Castform
RoamingPokemon{}={Species=[0x1109A4, 0x13431C], Level=[0x13427A, 0x13428A]} // Latias
StaticFirstBattleTweak=hardcoded_statics/rs_firstbattle
ShopItemOffsets=[0x14BAF0, 0x14BED4, 0x152F4C, 0x152F68, 0x1535F0, 0x153894, 0x153930, 0x153E84, 0x155280, 0x1552AC, 0x1563D8, 0x157388, 0x157BB0, 0x157BD8, 0x158030, 0x159EF4, 0x159F28, 0x159F58, 0x159F80, 0x159FE0, 0x15A004, 0x15A8F0, 0x15B1E4, 0x15BA70]
CRC32=BAFEDAE5

[Sapphire (U/E) 1.2]
Game=AXPE
Version=2
Type=Sapp
CopyTMText=1
CopyStaticPokemon=1
CopyFrom=Sapphire (E)
InstantTextTweak=instant_text/sapphire_12_instant_text
StaticFirstBattleTweak=hardcoded_statics/rs_firstbattle
CRC32=9CC4410E

[Emerald (U)]
Game=BPEE
Version=0
Type=Em
TableFile=gba_english
FreeSpace=0xE40000
PokemonCount=411
PokemonNameLength=11
PokemonMovesets=0x32937C
EggMoves=0x32ADD8
PokemonTMHMCompat=0x31E898
PokemonEvolutions=0x32531C
StarterPokemon=0x5B1DF8
StarterItems=0xB117A
TrainerData=0x310030
TrainerEntrySize=40
TrainerCount=0x357
TrainerClassNames=0x30FCD4
TrainerClassCount=66
TrainerClassNameLength=13
TrainerNameLength=12
DoublesTrainerClasses=[34, 46, 55, 56, 57]
EliteFourIndices=[261, 262, 263, 264, 335]
MossdeepStevenTeamOffset=0x5DD6D0
ItemEntrySize=44
ItemCount=376
MoveCount=354
MoveDescriptions=0x61C524
MoveNameLength=13
AbilityNameLength=13
TmMoves=0x615B94
TmMovesDuplicate=0x616040
MoveTutorData=0x61500C
MoveTutorMoves=30
ItemImages=0x614410
TmPals=[0xDB5E94, 0xDB5DF4, 0xDB604C, 0xDB5EBC, 0xDB5FD4, 0xDB6024, 0xDB5F0C, 0xDB5FFC, 0xDB5F84, 0xDB5FFC, 0xDB5F34, 0xDB5E44, 0xDB5F0C, 0xDB5FAC, 0xDB5E6C, 0xDB5EE4, 0xDB5E1C, 0xDB5F5C]
IntroCryOffset=0x30B0C
IntroSpriteOffset=0x31924
ItemBallPic=59
TradeTableOffset=0x338ED0
TradeTableSize=4
TradesUnused=[]
RunIndoorsTweakOffset=0x11A1E8
InstantTextTweak=instant_text/em_instant_text
CatchingTutorialOpponentMonOffset=0xB0870
CatchingTutorialPlayerMonOffset=0x139472
PCPotionOffset=0x5DFEFC
PickupTableStartLocator=0D000E0016000300560055
PickupItemCount=29
TypeEffectivenessOffset=0x31ACE8
DeoxysStatPrefix=190A1E0A230A280A
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x211A1C, 0x211A41, 0x211A44, 0x211AC6, 0x211AD4], Level=[0x211A46]} // Lileep
StaticPokemon{}={Species=[0x211A2E, 0x211AE4, 0x211AE7, 0x211B69, 0x211B77], Level=[0x211AE9]} // Anorith
StaticPokemon{}={Species=[0x23B032, 0x23B040, 0x23B095, 0x1E5982, 0x1E5A02, 0x1E5ABE, 0x1E5B3E], Level=[0x23B042]} // Kyogre
StaticPokemon{}={Species=[0x23B103, 0x23B111, 0x23B166, 0x1E59C2, 0x1E5AFE], Level=[0x23B113]} // Groudon
StaticPokemon{}={Species=[0x22DA06, 0x22DA0F, 0x22DA55], Level=[0x22DA11]} // Regirock
StaticPokemon{}={Species=[0x238F5C, 0x238F65, 0x238FAB], Level=[0x238F67]} // Regice
StaticPokemon{}={Species=[0x23905E, 0x239067, 0x2390AD], Level=[0x239069]} // Registeel
StaticPokemon{}={Species=[0x239725, 0x23972E, 0x239774, 0x2397C3, 0x2397E1, 0x1E5C60, 0x1E5C7E, 0x1E5D14, 0x1E5D32], Level=[0x239730]} // Rayquaza
StaticPokemon{}={Species=[0x272384, 0x27238D], Level=[0x27238F]} // Kecleons on OW (7)
StaticPokemon{}={Species=[0x1F56D6, 0x1F56DF], Level=[0x1F56E1]} // Kecleon w/ Steven
StaticPokemon{}={Species=[0x2377B2, 0x2377B9], Level=[0x2377B4]} // Voltorb 1
StaticPokemon{}={Species=[0x2377FF, 0x237806], Level=[0x237801]} // Voltorb 2
StaticPokemon{}={Species=[0x23784C, 0x237853], Level=[0x23784E]} // Voltorb 3
StaticPokemon{}={Species=[0x2339EE, 0x2339F5], Level=[0x2339F0]} // Electrode 1
StaticPokemon{}={Species=[0x233A3B, 0x233A42], Level=[0x233A3D]} // Electrode 2
StaticPokemon{}={Species=[0x242D1B, 0x242D29], Level=[0x242D2B]} // Sudowoodo in Battle Frontier
StaticPokemon{}={Species=[0x242A92, 0x242BA7], Level=[0x242BAC]} // Latios on Southern Island
StaticPokemon{}={Species=[0x242A9D, 0x242BBA], Level=[0x242BBF]} // Latias on Southern Island
StaticPokemon{}={Species=[0x267FE7, 0x267FF7, 0x268041, 0x26804C], Level=[0x267FFC]} // Deoxys on Birth Island
StaticPokemon{}={Species=[0x267E0D, 0x267E47, 0x267E9C, 0x267EA7], Level=[0x267E4C]} // Mew on Faraway Island
StaticPokemon{}={Species=[0x26919F, 0x2691CE, 0x26921D, 0x269228], Level=[0x2691D3]} // Ho-Oh on Navel Rock
StaticPokemon{}={Species=[0x2692E7, 0x2692F2, 0x26933C, 0x269347], Level=[0x2692F7]} // Lugia on Navel Rock
StaticPokemon{}={Species=[0x1EA783]} // Wynaut Egg
StaticPokemon{}={Species=[0x222868, 0x22286B, 0x2228ED, 0x2228FE], Level=[0x22286D]} // Beldum
StaticPokemon{}={Species=[0x270058, 0x27005B, 0x2700E7], Level=[0x27005D]} // Castform
RoamingPokemon{}={Species=[0x161BB0], Level=[0x161BE2, 0x161BEE]} // Latios
RoamingPokemon{}={Species=[0x161BB8], Level=[0x161BE2, 0x161BEE]} // Latias
StaticEggPokemonOffsets=[22]
StaticFirstBattleTweak=hardcoded_statics/em_firstbattle
StaticFirstBattleSpeciesOffset=0xFE003C
StaticFirstBattleLevelOffset=0xFE0008
StaticFirstBattleOffset=25
CreateInitialRoamerMonFunctionStartOffset=0x161B94
StaticSouthernIslandOffsets=[16, 17]
TMText[]=[3,15,0,1,0xA9,The TECHNICAL MACHINE I handed you contains [move].\p… … … … … …]
TMText[]=[4,14,0,1,0xB8,TATE: That TM04 contains... LIZA: [move]!\pTATE: It’s a move that’s perfect... LIZA: For any POKéMON!\p… … … … … …]
TMText[]=[5,0,29,12,0x0D,All my POKéMON does is [move]... No one dares to come near me...\pSigh... If you would, please take this TM away...]
TMText[]=[5,0,29,12,0x2F,TM05 contains [move].]
TMText[]=[8,3,3,1,0xAC,That TM08 contains [move].\p… … … … … …]
TMText[]=[9,0,19,32,0x0D,I like filling my mouth with seeds, then spitting them out fast!\pI like you, so you can have this!\pUse it on a POKéMON, and it will learn [move].\pWhat does that have to do with firing seeds? Well, nothing!]
TMText[]=[24,0,2,8,0x4C,WATTSON: Wahahahaha!\pI knew it, \v01\v05! I knew I’d made the right choice asking you!\pThis is my thanks - a TM containing [move]!\pGo on, you’ve earned it!]
TMText[]=[31,15,5,1,0x2F,TM31 contains [move]! It’s a move so horrible that I can’t describe it.]
TMText[]=[34,10,0,1,0xBB,That TM34 there contains [move]. You can count on it!\p… … … … … …]
TMText[]=[39,11,3,1,0x8F,That TM39 contains [move].\pIf you use a TM, it instantly teaches the move to a POKéMON.\pRemember, a TM can be used only once, so think before you use it.]
TMText[]=[40,12,1,1,0x97,TM40 contains [move].\p… … … … … …]
TMText[]=[41,9,2,2,0x2F,That’s, like, TM41, you know? Hey, it’s [move], you hearing me?\pHey, now, you listen here, like, I’m not laying a torment on you!]
TMText[]=[42,8,1,1,0x4FD,DAD: TM42 contains [move].\pIt might be able to turn a bad situation into an advantage.]
TMText[]=[47,24,10,1,0x19,STEVEN: Okay, thank you.\pYou went through all this trouble to deliver that. I need to thank you.\pLet me see... I’ll give you this TM.\pIt contains my favorite move, [move].]
TMText[]=[50,4,1,1,0xAA,That TM50 contains [move].]
MoveTutorText[]=[4,15,2,4,0x0D,Sigh…\pSOOTOPOLIS’s GYM LEADER is really lovably admirable.\pBut that also means I have many rivals for his attention.\pHe’s got appeal with a [move]. I couldn’t even catch his eye.\pPlease, let me teach your POKéMON the move [move]!]
MoveTutorText[]=[4,15,2,4,0x30,Okay, which POKéMON should I teach [move]?]
MoveTutorText[]=[15,0,6,15,0x0D,I can’t do this anymore!\pIt’s utterly hopeless!\pI’m a FIGHTING-type TRAINER, so I can’t win at the MOSSDEEP GYM no matter how hard I try!\pArgh! Punch! Punch! Punch! Punch! Punch! Punch!\pWhat, don’t look at me that way! I’m only hitting the ground!\pOr do you want me to teach your POKéMON [move]?]
MoveTutorText[]=[15,0,6,15,0x60,I want you to win at the MOSSDEEP GYM using that [move]!]
MoveTutorText[]=[12,7,0,5,0x0D,I don’t intend to be going nowhere fast in the sticks like this forever.\pYou watch me, I’ll get out to the city and become a huge hit.\pSeriously, I’m going to cause a huge explosion of popularity!\pIf you overheard that, I’ll happily teach [move] to your POKéMON!]
MoveTutorText[]=[12,7,0,5,0x30,Fine! [move] it is! Which POKéMON wants to learn it?]
MoveTutorText[]=[12,7,0,5,0x60,For a long time, I’ve taught POKéMON how to use [move], but I’ve yet to ignite my own explosion…\pMaybe it’s because deep down, I would rather stay here…]
MoveTutorText[]=[29,6,4,4,0x0D,There’s a move that is wickedly cool.\pIt’s called [move].\nWant me to teach it to a POKéMON?]
MoveTutorText[]=[8,5,0,5,0x0D,I want all sorts of things! But I used up my allowance…\pWouldn’t it be nice if there were a spell that made money appear when you waggle a finger?\pIf you want, I can teach your POKéMON the move [move].\pMoney won’t appear, but your POKéMON will do well in battle. Yes?]
MoveTutorText[]=[8,5,0,5,0x60,When a POKéMON uses [move], all sorts of nice things happen.]
MoveTutorText[]=[7,4,3,3,0x0D,Ah, young one!\pI am also a young one, but I mimic the styles and speech of the elderly folks of this town.\pWhat do you say, young one? Would you agree to it if I were to offer to teach the move [move]?]
MoveTutorText[]=[7,4,3,3,0x60,[move] is a move of great depth.\pCould you execute it to perfection as well as me…?]
MoveTutorText[]=[7,4,3,3,0x56,Oh, boo! I wanted to teach [move] to your POKéMON!]
MoveTutorText[]=[16,0,2,10,0x0D,Did you know that you can go from here a long way in that direction without changing direction?\pI might even be able to roll that way.\pDo you think your POKéMON will want to roll, too?\pI can teach one the move [move] if you’d like.]
MoveTutorText[]=[24,12,5,2,0x0D,Humph! My wife relies on HIDDEN POWER to stay awake.\pShe should just take a nap like I do.\pI can teach your POKéMON how to [move]. Interested?]
MoveTutorText[]=[24,12,5,2,0x60,I’ve never once gotten my wife’s coin trick right.\pI would be happy if I got it right even as I teach [move]…]
MoveTutorText[]=[14,13,21,4,0x0D,When I see the wide world from up here on the roof…\pI think about how nice it would be if there were more than just one me so I could enjoy all sorts of lives.\pOf course it’s not possible. Giggle…\pI know! Would you be interested in having a POKéMON learn [move]?]
MoveTutorText[]=[14,13,21,4,0x30,Giggle… Which POKéMON do you want me to teach [move]?]
MoveTutorText[]=[14,13,21,4,0x56,Oh, no?\pA POKéMON can do well in a battle using it, you know.]
MoveTutorText[]=[25,9,6,9,0x0D,Heh! My POKéMON totally rules! It’s cooler than any POKéMON!\pI was lipping off with a swagger in my step like that when the CHAIRMAN chewed me out.\pThat took the swagger out of my step.\pIf you’d like, I’ll teach the move [move] to a POKéMON of yours.]
MoveTutorText[]=[25,9,6,9,0x30,All right, which POKéMON wants to learn how to [move]?]
MoveTutorText[]=[25,9,6,9,0x60,I’ll just praise my POKéMON from now on without the [move].]
SpecialMusicStatics=[151,249,250,382,383,384,386]
NewIndexToMusicTweak=musicfix/em_musicfix
NewIndexToMusicPoolOffset=0xFE014C
ShopItemOffsets=[0x1DCDD4, 0x1DD1F0, 0x1FC260, 0x1FC27C, 0x1FE4F0, 0x1FF9E8, 0x1FFCD8, 0x2025A0, 0x207D8C, 0x207DB8, 0x20DC60, 0x211100, 0x214F30, 0x214F58, 0x217680, 0x21FB60, 0x21FB94, 0x21FC7C, 0x21FCA4, 0x21FE20, 0x21FE44, 0x2223E0, 0x2267AC, 0x229624, 0x267AE4, 0x2683E8, 0x268414]
SkipShops=[1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24, 25, 26]
MainGameShops=[0, 4, 17, 18]
CRC32=1F1C08FB

[Fire Red (U) 1.0]
Game=BPRE
Version=0
Type=FRLG
TableFile=gba_english
FreeSpace=0x800000
PokemonCount=411
PokemonNameLength=11
PokemonMovesets=0x25D7B4
EggMoves=0x25EF0C
PokemonTMHMCompat=0x252BC8
PokemonEvolutions=0x259754
BattleTrappersBanned=[55,56,57,58,59]
StarterPokemon=0x169BB5
TrainerData=0x23EAC8
TrainerEntrySize=40
TrainerCount=0x2E7
TrainerClassNames=0x23E558
TrainerClassCount=107
TrainerClassNameLength=13
TrainerNameLength=12
DoublesTrainerClasses=[26, 40, 52, 53, 54, 92, 93, 94, 95, 96]
EliteFourIndices=[410, 411, 412, 413, 438, 439, 440]
ItemEntrySize=44
ItemCount=374
MoveCount=354
MoveDescriptions=0x4886E8
MoveNameLength=13
AbilityNameLength=13
TmMoves=0x45A5A4
TmMovesDuplicate=0x45A80C
MoveTutorData=0x459B60
MoveTutorMoves=15
ItemImages=0x3D4294
TmPals=[0xE91E64, 0xE91DC4, 0xE9201C, 0xE91E8C, 0xE91FA4, 0xE91FF4, 0xE91EDC, 0xE91FCC, 0xE91F54, 0xE91FCC, 0xE91F04, 0xE91E14, 0xE91EDC, 0xE91F7C, 0xE91E3C, 0xE91EB4, 0xE91DEC, 0xE91F2C]
IntroCryOffset=0x12FB38
IntroSpriteOffset=0x130FA0
IntroOtherOffset=0x130F4C
ItemBallPic=92
TradeTableOffset=0x26CF8C
TradeTableSize=9
TradesUnused=[]
RunIndoorsTweakOffset=0xBD494
InstantTextTweak=instant_text/fr_10_instant_text
CatchingTutorialOpponentMonOffset=0x7F88C
PCPotionOffset=0x402220
PickupTableStartLocator=8B000F00850019008600230087002D
PickupItemCount=16
TypeEffectivenessOffset=0x24F050
DeoxysStatPrefix=7F002301FFFF
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x16C472, 0x16C475, 0x16C4B5, 0x16C4E9], Level=[0x16C477]} // Eevee in Celadon Condominiums
StaticPokemon{}={Species=[0x16EC0C, 0x16EC13], Level=[0x16EC86]} // Hitmonlee in Fighting Dojo
StaticPokemon{}={Species=[0x16EC52, 0x16EC59], Level=[0x16EC86]} // Hitmonchan in Fighting Dojo
StaticPokemon{}={Species=[0x163840, 0x163847], Level=[0x163842]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x16389E, 0x1638A5], Level=[0x1638A0]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x1637CC, 0x1637D3, 0x163827], Level=[0x1637CE]} // Zapdos in The Power Plant
StaticPokemon{}={Species=[0x1631C0, 0x1631C7, 0x16321B, 0x17009D, 0x1700A9], Level=[0x1631C2]} // Articuno (Seafoam and Route 15 Gatehouse)
StaticPokemon{}={Species=[0x163B47, 0x163B4E, 0x163BA2], Level=[0x163B49]} // Moltres in Mt.Ember
StaticPokemon{}={Species=[0x16250A, 0x16251E, 0x162564], Level=[0x162520]} // Mewtwo in Unk. Dungeon
StaticPokemon{}={Species=[0x168049, 0x168050, 0x160CA2, 0x160CA8], Level=[0x16804B]} // Snorlax (Route 12 and S.S. Anne)
StaticPokemon{}={Species=[0x168156, 0x16815D], Level=[0x168158]} // Snorlax (Route 16)
StaticPokemon{}={Species=[0x163CBC, 0x163CC2], Level=[0x163CC4]} // Hypno in Berry Forest
StaticPokemon{}={Species=[0x1652E6, 0x1652F6, 0x165340, 0x16534B], Level=[0x1652FB]} // Deoxys on Birth Island
StaticPokemon{}={Species=[0x16503C, 0x16506B, 0x1650BA, 0x1650C5], Level=[0x165070]} // Ho-Oh on Navel Rock
StaticPokemon{}={Species=[0x16518A, 0x165195, 0x1651DF, 0x1651EA], Level=[0x16519A]} // Lugia on Navel Rock
StaticPokemon{}={Species=[0x16E7E5, 0x16E7E9, 0x16E7F4, 0x16E6E6], Level=[0x16E7F6]} // Old Amber
StaticPokemon{}={Species=[0x16E75B, 0x16E75F, 0x16E76A, 0x16E66A], Level=[0x16E76C]} // Helix Fossil
StaticPokemon{}={Species=[0x16E7A0, 0x16E7A4, 0x16E7AF, 0x16E6A8], Level=[0x16E7B1]} // Dome Fossil
StaticPokemon{}={Species=[0x161ADE, 0x161AE1, 0x161B20, 0x161B53], Level=[0x161AE3]} // Lapras in Silph. Co
StaticPokemon{}={Species=[0x16F7C3, 0x16F7C6, 0x16F885], Level=[0x16F7C8]} // Magikarp in Mt.Moon Center
StaticPokemon{}={Species=[0x16CC18, 0x16CC94], Level=[0x16CCD7]} // Abra
StaticPokemon{}={Species=[0x16CC28, 0x16CC9F], Level=[0x16CCEC]} // Clefairy
StaticPokemon{}={Species=[0x16CC48, 0x16CCB5], Level=[0x16CD16]} // Scyther
StaticPokemon{}={Species=[0x16CC38, 0x16CCAA], Level=[0x16CD01]} // Dratini
StaticPokemon{}={Species=[0x16CC58, 0x16CCC0], Level=[0x16CD2B]} // Porygon
RoamingPokemon{}={Species=[0xA80224, 0x4642FC], Level=[0x141CC8, 0x141CDC]} // Raikou
RoamingPokemon{}={Species=[0xA8021C, 0x4642F4], Level=[0x141CC8, 0x141CDC]} // Entei
RoamingPokemon{}={Species=[0xA80220, 0x4642F8], Level=[0x141CC8, 0x141CDC]} // Suicune
RoamingPokemonTweak=hardcoded_statics/roamers/fr_roamers_10
FossilLevelOffsets=[0x16E7F6, 0x16E76C, 0x16E7B1]
GhostMarowakTweak=hardcoded_statics/fr_marowak_10
GhostMarowakSpeciesOffsets=[0xA80024, 0x1634D2, 0x1634FD]
GhostMarowakLevelOffsets=[0xA80012, 0x1634D4]
GhostMarowakGenderOffset=0xA80004
GhostMarowakOffset=25
TMText[]=[3,7,5,3,0x2A,TM03 teaches [move].\pUse it on a worthy POKéMON!]
TMText[]=[4,14,3,7,0x2A,TM04 is [move].]
TMText[]=[6,11,3,7,0x91,Sealed within that TM06 lies [move]!\pIt is a secret technique dating back some four hundred years.]
TMText[]=[16,10,5,2,0x316,TM16 contains [move].]
TMText[]=[19,10,16,7,0x9E,TM19 contains [move].\pWouldn’t you agree that it’s a wonderful move?]
TMText[]=[20,10,5,2,0x322,TM20 contains [move].]
TMText[]=[26,5,1,8,0x9E,TM26 contains [move].\pIt is a powerful technique.\pI made it when I ran the GYM here, far too long ago…]
TMText[]=[27,23,1,1,0x50,TM27 is a move called [move]…\pIf you treat your POKéMON good, it will return your love by working its hardest in battle.]
TMText[]=[28,7,1,1,0x0D,Those miserable ROCKETS!\pLook what they’ve done to my house!\pThey stole a TM for teaching POKéMON how to [move]!\pThat cost me a bundle, it did!]
TMText[]=[28,7,1,1,0x17,I figure what’s lost is lost.\pI decided to catch a POKéMON that could [move] without a TM.]
TMText[]=[29,14,8,1,0x41,You already know, don’t you? TM29 is [move].]
TMText[]=[33,10,5,2,0x32E,TM33 contains [move].]
TMText[]=[34,9,6,1,0x9E,TM34 contains [move]!\pTeach it to your favorite POKéMON!]
TMText[]=[38,12,0,8,0x2A,TM38 contains [move].\nTeach it to strong POKéMON.]
TMText[]=[38,12,0,8,0x99,[move] is the ultimate\ntechnique.\pDon't waste it on weak POKéMON.]
TMText[]=[39,6,2,1,0x99,A TM, Technical Machine, contains a technique for POKéMON.\pUsing a TM teaches the move it contains to a POKéMON.\pA TM is good for only one use.\pSo, when you use one, pick the POKéMON carefully.\pAnyways… TM39 contains [move].]
MoveTutorText[]=[0,3,22,5,0x0D,A hit of roaring ferocity!\pPacked with destructive power!\pWhen the chips are down, [move] is the ultimate attack! You agree, yes?\pNow! Let me teach it to your POKéMON!]
MoveTutorText[]=[0,3,22,5,0x60,Now, we are comrades in the way of [move]!\pYou should go before you’re seen by the misguided fool who trains only silly moves over there.]
MoveTutorText[]=[0,3,22,5,0x56,You’ll be back when you understand the worth of [move].]
MoveTutorText[]=[1,3,17,1,0x0D,Not many people come out here.\pIf I train here, I’m convinced that I’ll get stronger and stronger.\pYep, stronger and stronger…\pHow would you like to learn a strong move? It’s [move].]
MoveTutorText[]=[2,3,22,6,0x0D,A hit of brutal ferocity!\pPacked with destructive power!\pWhen you get right down to it, [move] is the ultimate attack! Don’t you agree?\pOkay! I’ll teach it to your POKéMON!]
MoveTutorText[]=[2,3,22,6,0x60,Now, we are soul mates in the way of [move]!\pYou should run before you’re seen by the deluded nitwit who trains only simple moves over there.]
MoveTutorText[]=[2,3,22,6,0x56,You’ll come crawling back when you realize the value of [move].]
MoveTutorText[]=[3,35,3,1,0x0D,Ready? Boing!\pWe’re having a wrestling match to see who wimps out first.\pIf you were to join us, you’d be squashed like a bug, though…\pHow about I teach [move] to a POKéMON of yours instead?]
MoveTutorText[]=[3,35,3,1,0x30,Which POKéMON wants to learn how to [move]?]
MoveTutorText[]=[4,1,40,13,0x0D,You should be proud of yourself, having battled your way through VICTORY ROAD so courageously.\pIn recognition of your feat, I’ll teach you [move].\pWould you like me to teach that technique?]
MoveTutorText[]=[4,1,40,13,0x30,Which POKéMON should I teach [move]?]
MoveTutorText[]=[5,10,2,1,0x0D,Oh, hi! I finally finished POKéMON.\pNot done yet? How about I teach you a good move?\pThe move I have in mind is [move].]
MoveTutorText[]=[5,10,2,1,0x30,Which POKéMON should I teach [move] to?]
MoveTutorText[]=[5,10,2,1,0x60,Are you using that [move] move I taught your POKéMON?]
MoveTutorText[]=[6,6,0,6,0x0D,The secrets of space… The mysteries of earth…\pThere are so many things about which we know so little.\pBut that should spur us to study harder, not toss in the towel.\pThe only thing you should toss…\pWell, how about [move]? Should I teach that to a POKéMON?]
MoveTutorText[]=[6,6,0,6,0x30,Which POKéMON wants to learn [move]?]
MoveTutorText[]=[7,14,1,4,0x0B,Oh wow! A POKé DOLL!\pFor me? Thank you!\pYou know what? I can teach the move [move].]
MoveTutorText[]=[7,14,1,4,0x2E,I really love [move]! Who’s going to learn it?]
MoveTutorText[]=[7,14,1,4,0x5B,Don’t you like [move]?]
MoveTutorText[]=[8,12,3,1,0x0D,Tch-tch-tch! I’ll teach you a nifty move.\pTeach it to a POKéMON, and watch the fun unfold!\pIt’s a move called [move]. Does it strike your fancy?]
MoveTutorText[]=[8,12,3,1,0x60,Tch-tch-tch! That’s the sound of a metronome.\pIt inspired me to start teaching [move] to interested trainers.]
MoveTutorText[]=[9,3,6,7,0x0D,Hello, there!\pI’ve seen you about, but I never had a chance to chat.\pIt must be good luck that brought us together finally.\pI’d like to celebrate by teaching you the move [move].]
MoveTutorText[]=[9,3,6,7,0x30,So, who’s the POKéMON that gets the chance to learn [move]?]
MoveTutorText[]=[10,3,1,1,0x0D,Yawn! I must have dozed off in the sun.\pI had this weird dream about a DROWZEE eating my dream.\pAnd… I learned how to teach [move]…\pOogh, this is too spooky!\pLet me teach it to a POKéMON so I can forget about it!]
MoveTutorText[]=[10,3,1,1,0x30,Which POKéMON wants to learn [move]?]
MoveTutorText[]=[11,1,48,5,0x0D,Eeek! No! Stop! Help!\pOh, you’re not with TEAM ROCKET. I’m sorry, I thought…\pWill you forgive me if I teach you the [move] technique?]
MoveTutorText[]=[11,1,48,5,0x30,Which POKéMON should I teach [move]?]
MoveTutorText[]=[11,1,48,5,0x60,[move] is a useful move, but it might not work on some POKéMON.]
MoveTutorText[]=[11,1,48,5,0x56,Oh… But [move] is convenient…]
MoveTutorText[]=[12,1,97,1,0x0D,Can you imagine? If this volcano were to erupt?\pThe explosion would be the end of us. How terrifying is that?\pWhile we’re terrified, would you like me to teach [move]?]
MoveTutorText[]=[12,1,97,1,0x30,You’re terribly brave!\pWhich POKéMON should I teach [move]?]
MoveTutorText[]=[12,1,97,1,0x60,Using [move] while on this volcano…\pWhat a terrifying thrill!]
MoveTutorText[]=[13,1,82,9,0x0D,When you’re up on a rocky mountain like this, rockslides are a threat.\pCan you imagine? Boulders tumbling down on you?\pThat’d be, like, waaaaaaaaaaah! Total terror!\pYou don’t seem to be scared. Want to try using [move]?]
MoveTutorText[]=[13,1,82,9,0x30,Which POKéMON should I teach [move]?]
MoveTutorText[]=[14,3,7,15,0x0D,Aww, I wish I was a KANGASKHAN baby.\pI’d love to be a substitute for the baby…\pAnd snuggle in the mother KANGASKHAN’s belly pouch.\pBut only POKéMON can do that…\pOn an unrelated note, want me to teach [move] to one of your POKéMON?]
MoveTutorText[]=[14,3,7,15,0x30,Which POKéMON wants to learn [move]?]
MoveTutorText[]=[14,3,7,15,0x56,Oh, really? [move] seems so fun…]
SpecialMusicStatics=[144,145,146,150,249,250,386]
NewIndexToMusicTweak=musicfix/fr_musicfix_10
NewIndexToMusicPoolOffset=0xA80140
ShopItemOffsets=[0x1649B8, 0x1676E4, 0x1676FC, 0x167718, 0x167738, 0x16A298, 0x16A708, 0x16ACD8, 0x16B390, 0x16B68C, 0x16BB38, 0x16BB74, 0x16BC30, 0x16BC84, 0x16BCBC, 0x16D518, 0x16EA48, 0x16EAF4, 0x16EFDC, 0x170B58, 0x1718B4, 0x171CD4, 0x171E8C]
SkipShops=[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 15, 16, 17, 18, 19, 20, 21, 22]
MainGameShops=[12, 13, 14]
CRC32=DD88761C

[Leaf Green (U) 1.0]
Game=BPGE
Version=0
Type=FRLG
CopyTMText=1
CopyFrom=Fire Red (U) 1.0
PokemonMovesets=0x25D794
EggMoves=0x25EEEC
PokemonTMHMCompat=0x252BA4
PokemonEvolutions=0x259734
StarterPokemon=0x169B91
TrainerData=0x23EAA4
TrainerClassNames=0x23E534
MoveDescriptions=0x487FC4
TmMoves=0x459FC4
TmMovesDuplicate=0x45A22C
MoveTutorData=0x459580
ItemImages=0x3D40D0
TmPals=[0xE91EE4, 0xE91E44, 0xE9209C, 0xE91F0C, 0xE92024, 0xE92074, 0xE91F5C, 0xE9204C, 0xE91FD4, 0xE9204C, 0xE91F84, 0xE91E94, 0xE91F5C, 0xE91FFC, 0xE91EBC, 0xE91F34, 0xE91E6C, 0xE91FAC]
IntroCryOffset=0x12FB10
IntroSpriteOffset=0x130F78
IntroOtherOffset=0x130F24
TradeTableOffset=0x26CF6C
RunIndoorsTweakOffset=0xBD468
InstantTextTweak=instant_text/lg_10_instant_text
CatchingTutorialOpponentMonOffset=0x7F860
PCPotionOffset=0x40205C
TypeEffectivenessOffset=0x24F02C
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x16C44E, 0x16C451, 0x16C491, 0x16C4C5], Level=[0x16C453]} // Eevee in Celadon Condominiums
StaticPokemon{}={Species=[0x16EBE8, 0x16EBEF], Level=[0x16EC62]} // Hitmonlee in Fighting Dojo
StaticPokemon{}={Species=[0x16EC2E, 0x16EC35], Level=[0x16EC62]} // Hitmonchan in Fighting Dojo
StaticPokemon{}={Species=[0x16381C, 0x163823], Level=[0x16381E]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x16387A, 0x163881], Level=[0x16387C]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x1637A8, 0x1637AF, 0x163803], Level=[0x1637AA]} // Zapdos in The Power Plant
StaticPokemon{}={Species=[0x16319C, 0x1631A3, 0x1631F7, 0x170079, 0x170085], Level=[0x16319E]} // Articuno (Seafoam and Route 15 Gatehouse)
StaticPokemon{}={Species=[0x163B23, 0x163B2A, 0x163B7E], Level=[0x163B25]} // Moltres in Mt.Ember
StaticPokemon{}={Species=[0x1624E6, 0x1624FA, 0x162540], Level=[0x1624FC]} // Mewtwo in Unk. Dungeon
StaticPokemon{}={Species=[0x168025, 0x16802C, 0x160C7E, 0x160C84], Level=[0x168027]} // Snorlax (Route 12 and S.S. Anne)
StaticPokemon{}={Species=[0x168132, 0x168139], Level=[0x168134]} // Snorlax (Route 16)
StaticPokemon{}={Species=[0x163C98, 0x163C9E], Level=[0x163CA0]} // Hypno in Berry Forest
StaticPokemon{}={Species=[0x1652C2, 0x1652D2, 0x16531C, 0x165327], Level=[0x1652D7]} // Deoxys on Birth Island
StaticPokemon{}={Species=[0x165018, 0x165047, 0x165096, 0x1650A1], Level=[0x16504C]} // Ho-Oh on Navel Rock
StaticPokemon{}={Species=[0x165166, 0x165171, 0x1651BB, 0x1651C6], Level=[0x165176]} // Lugia on Navel Rock
StaticPokemon{}={Species=[0x16E7C1, 0x16E7C5, 0x16E7D0, 0x16E6C2], Level=[0x16E7D2]} // Old Amber
StaticPokemon{}={Species=[0x16E737, 0x16E73B, 0x16E746, 0x16E646], Level=[0x16E748]} // Helix Fossil
StaticPokemon{}={Species=[0x16E77C, 0x16E780, 0x16E78B, 0x16E684], Level=[0x16E78D]} // Dome Fossil
StaticPokemon{}={Species=[0x161ABA, 0x161ABD, 0x161AFC, 0x161B2F], Level=[0x161ABF]} // Lapras in Silph. Co
StaticPokemon{}={Species=[0x16F79F, 0x16F7A2, 0x16F861], Level=[0x16F7A4]} // Magikarp in Mt.Moon Center
StaticPokemon{}={Species=[0x16CBF4, 0x16CC70], Level=[0x16CCB3]} // Abra
StaticPokemon{}={Species=[0x16CC04, 0x16CC7B], Level=[0x16CCC8]} // Clefairy
StaticPokemon{}={Species=[0x16CC14, 0x16CCA7], Level=[0x16CD1C]} // Pinsir
StaticPokemon{}={Species=[0x16CC24, 0x16CC86], Level=[0x16CCDD]} // Dratini
StaticPokemon{}={Species=[0x16CC34, 0x16CC9C], Level=[0x16CD07]} // Porygon
RoamingPokemon{}={Species=[0xA80224, 0x463D1C], Level=[0x141CA0, 0x141CB4]} // Raikou
RoamingPokemon{}={Species=[0xA8021C, 0x463D14], Level=[0x141CA0, 0x141CB4]} // Entei
RoamingPokemon{}={Species=[0xA80220, 0x463D18], Level=[0x141CA0, 0x141CB4]} // Suicune
RoamingPokemonTweak=hardcoded_statics/roamers/lg_roamers_10
FossilLevelOffsets=[0x16E7D2, 0x16E748, 0x16E78D]
GhostMarowakTweak=hardcoded_statics/lg_marowak_10
GhostMarowakSpeciesOffsets=[0xA80024, 0x1634AE, 0x1634D9]
GhostMarowakLevelOffsets=[0xA80012, 0x1634B0]
GhostMarowakGenderOffset=0xA80004
SpecialMusicStatics=[144,145,146,150,249,250,386]
NewIndexToMusicTweak=musicfix/lg_musicfix_10
NewIndexToMusicPoolOffset=0xA80140
ShopItemOffsets=[0x164994, 0x1676C0, 0x1676D8, 0x1676F4, 0x167714, 0x16A274, 0x16A6E4, 0x16ACB4, 0x16B36C, 0x16B668, 0x16BB14, 0x16BB50, 0x16BC0C, 0x16BC60, 0x16BC98, 0x16D4F4, 0x16EA24, 0x16EAD0, 0x16EFB8, 0x170B34, 0x171890, 0x171CB0, 0x171E68]
CRC32=D69C96CC

[Fire Red (U) 1.1]
Game=BPRE
Version=1
Type=FRLG
CopyTMText=1
CopyFrom=Fire Red (U) 1.0
PokemonMovesets=0x25D824
EggMoves=0x25EF7C
PokemonTMHMCompat=0x252C38
PokemonEvolutions=0x2597C4
StarterPokemon=0x169C2D
TrainerData=0x23EB38
TrainerClassNames=0x23E5C8
MoveDescriptions=0x488748
TmMoves=0x45A604
TmMovesDuplicate=0x45A86C
MoveTutorData=0x459BC0
ItemImages=0x3D4304
TmPals=[0xE91E64, 0xE91DC4, 0xE9201C, 0xE91E8C, 0xE91FA4, 0xE91FF4, 0xE91EDC, 0xE91FCC, 0xE91F54, 0xE91FCC, 0xE91F04, 0xE91E14, 0xE91EDC, 0xE91F7C, 0xE91E3C, 0xE91EB4, 0xE91DEC, 0xE91F2C]
IntroCryOffset=0x12FBB0
IntroSpriteOffset=0x131018
IntroOtherOffset=0x130FC4
TradeTableOffset=0x26CFFC
RunIndoorsTweakOffset=0xBD4A8
TextSpeedValuesOffset=0x41F498
InstantTextTweak=instant_text/fr_11_instant_text
CatchingTutorialOpponentMonOffset=0x7F8A0
PCPotionOffset=0x402290
TypeEffectivenessOffset=0x24F0C0
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x16C4EA, 0x16C4ED, 0x16C52D, 0x16C561], Level=[0x16C4EF]} // Eevee in Celadon Condominiums
StaticPokemon{}={Species=[0x16EC84, 0x16EC8B], Level=[0x16ECFE]} // Hitmonlee in Fighting Dojo
StaticPokemon{}={Species=[0x16ECCA, 0x16ECD1], Level=[0x16ECFE]} // Hitmonchan in Fighting Dojo
StaticPokemon{}={Species=[0x1638B8, 0x1638BF], Level=[0x1638BA]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x163916, 0x16391D], Level=[0x163918]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x163844, 0x16384B, 0x16389F], Level=[0x163846]} // Zapdos in The Power Plant
StaticPokemon{}={Species=[0x163238, 0x16323F, 0x163293, 0x170115, 0x170121], Level=[0x16323A]} // Articuno (Seafoam and Route 15 Gatehouse)
StaticPokemon{}={Species=[0x163BBF, 0x163BC6, 0x163C1A], Level=[0x163BC1]} // Moltres in Mt.Ember
StaticPokemon{}={Species=[0x162582, 0x162596, 0x1625DC], Level=[0x162598]} // Mewtwo in Unk. Dungeon
StaticPokemon{}={Species=[0x1680C1, 0x1680C8, 0x160D1A, 0x160D20], Level=[0x1680C3]} // Snorlax (Route 12 and S.S. Anne)
StaticPokemon{}={Species=[0x1681CE, 0x1681D5], Level=[0x1681D0]} // Snorlax (Route 16)
StaticPokemon{}={Species=[0x163D34, 0x163D3A], Level=[0x163D3C]} // Hypno in Berry Forest
StaticPokemon{}={Species=[0x16535E, 0x16536E, 0x1653B8, 0x1653C3], Level=[0x165373]} // Deoxys on Birth Island
StaticPokemon{}={Species=[0x1650B4, 0x1650E3, 0x165132, 0x16513D], Level=[0x1650E8]} // Ho-Oh on Navel Rock
StaticPokemon{}={Species=[0x165202, 0x16520D, 0x165257, 0x165262], Level=[0x165212]} // Lugia on Navel Rock
StaticPokemon{}={Species=[0x16E85D, 0x16E861, 0x16E86C, 0x16E75E], Level=[0x16E86E]} // Old Amber
StaticPokemon{}={Species=[0x16E7D3, 0x16E7D7, 0x16E7E2, 0x16E6E2], Level=[0x16E7E4]} // Helix Fossil
StaticPokemon{}={Species=[0x16E818, 0x16E81C, 0x16E827, 0x16E720], Level=[0x16E829]} // Dome Fossil
StaticPokemon{}={Species=[0x161B56, 0x161B59, 0x161B98, 0x161BCB], Level=[0x161B5B]} // Lapras in Silph. Co
StaticPokemon{}={Species=[0x16F83B, 0x16F83E, 0x16F8FD], Level=[0x16F840]} // Magikarp in Mt.Moon Center
StaticPokemon{}={Species=[0x16CC90, 0x16CD0C], Level=[0x16CD4F]} // Abra
StaticPokemon{}={Species=[0x16CCA0, 0x16CD17], Level=[0x16CD64]} // Clefairy
StaticPokemon{}={Species=[0x16CCC0, 0x16CD2D], Level=[0x16CD8E]} // Scyther
StaticPokemon{}={Species=[0x16CCB0, 0x16CD22], Level=[0x16CD79]} // Dratini
StaticPokemon{}={Species=[0x16CCD0, 0x16CD38], Level=[0x16CDA3]} // Porygon
RoamingPokemon{}={Species=[0xA80224, 0x46435C], Level=[0x141D40, 0x141D54]} // Raikou
RoamingPokemon{}={Species=[0xA8021C, 0x464354], Level=[0x141D40, 0x141D54]} // Entei
RoamingPokemon{}={Species=[0xA80220, 0x464358], Level=[0x141D40, 0x141D54]} // Suicune
RoamingPokemonTweak=hardcoded_statics/roamers/fr_roamers_11
FossilLevelOffsets=[0x16E86E, 0x16E7E4, 0x16E829]
GhostMarowakTweak=hardcoded_statics/fr_marowak_11
GhostMarowakSpeciesOffsets=[0xA80024, 0x16354A, 0x163575]
GhostMarowakLevelOffsets=[0xA80012, 0x16354C]
GhostMarowakGenderOffset=0xA80004
SpecialMusicStatics=[144,145,146,150,249,250,386]
NewIndexToMusicTweak=musicfix/fr_musicfix_11
NewIndexToMusicPoolOffset=0xA80140
ShopItemOffsets=[0x164A30, 0x16775C, 0x167774, 0x167790, 0x1677B0, 0x16A310, 0x16A780, 0x16AD50, 0x16B408, 0x16B704, 0x16BBB0, 0x16BBEC, 0x16BCA8, 0x16BCFC, 0x16BD34, 0x16D590, 0x16EAC0, 0x16EB6C, 0x16F054, 0x170BD0, 0x17192C, 0x171D4C, 0x171F04]
CRC32=84EE4776

[Leaf Green (U) 1.1]
Game=BPGE
Version=1
Type=FRLG
CopyTMText=1
CopyFrom=Leaf Green (U) 1.0
PokemonMovesets=0x25D804
EggMoves=0x25EF5C
PokemonTMHMCompat=0x252C14
PokemonEvolutions=0x2597A4
StarterPokemon=0x169C09
TrainerData=0x23EB14
TrainerClassNames=0x23E5A4
MoveDescriptions=0x488034
TmMoves=0x45A034
TmMovesDuplicate=0x45A29C
MoveTutorData=0x4595F0
ItemImages=0x3D4140
TmPals=[0xE91EE4, 0xE91E44, 0xE9209C, 0xE91F0C, 0xE92024, 0xE92074, 0xE91F5C, 0xE9204C, 0xE91FD4, 0xE9204C, 0xE91F84, 0xE91E94, 0xE91F5C, 0xE91FFC, 0xE91EBC, 0xE91F34, 0xE91E6C, 0xE91FAC]
IntroCryOffset=0x12FB88
IntroSpriteOffset=0x130FF0
IntroOtherOffset=0x130F9C
TradeTableOffset=0x26CFDC
RunIndoorsTweakOffset=0xBD47C
TextSpeedValuesOffset=0x41F2D4
InstantTextTweak=instant_text/lg_11_instant_text
CatchingTutorialOpponentMonOffset=0x7F874
PCPotionOffset=0x4020CC
TypeEffectivenessOffset=0x24F09C
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x16C4C6, 0x16C4C9, 0x16C509, 0x16C53D], Level=[0x16C4CB]} // Eevee in Celadon Condominiums
StaticPokemon{}={Species=[0x16EC60, 0x16EC67], Level=[0x16ECDA]} // Hitmonlee in Fighting Dojo
StaticPokemon{}={Species=[0x16ECA6, 0x16ECAD], Level=[0x16ECDA]} // Hitmonchan in Fighting Dojo
StaticPokemon{}={Species=[0x163894, 0x16389B], Level=[0x163896]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x1638F2, 0x1638F9], Level=[0x1638F4]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x163820, 0x163827, 0x16387B], Level=[0x163822]} // Zapdos in The Power Plant
StaticPokemon{}={Species=[0x163214, 0x16321B, 0x16326F, 0x1700F1, 0x1700FD], Level=[0x163216]} // Articuno (Seafoam and Route 15 Gatehouse)
StaticPokemon{}={Species=[0x163B9B, 0x163BA2, 0x163BF6], Level=[0x163B9D]} // Moltres in Mt.Ember
StaticPokemon{}={Species=[0x16255E, 0x162572, 0x1625B8], Level=[0x162574]} // Mewtwo in Unk. Dungeon
StaticPokemon{}={Species=[0x16809D, 0x1680A4, 0x160CF6, 0x160CFC], Level=[0x16809F]} // Snorlax (Route 12 and S.S. Anne)
StaticPokemon{}={Species=[0x1681AA, 0x1681B1], Level=[0x1681AC]} // Snorlax (Route 16)
StaticPokemon{}={Species=[0x163D10, 0x163D16], Level=[0x163D18]} // Hypno in Berry Forest
StaticPokemon{}={Species=[0x16533A, 0x16534A, 0x165394, 0x16539F], Level=[0x16534F]} // Deoxys on Birth Island
StaticPokemon{}={Species=[0x165090, 0x1650BF, 0x16510E, 0x165119], Level=[0x1650C4]} // Ho-Oh on Navel Rock
StaticPokemon{}={Species=[0x1651DE, 0x1651E9, 0x165233, 0x16523E], Level=[0x1651EE]} // Lugia on Navel Rock
StaticPokemon{}={Species=[0x16E839, 0x16E83D, 0x16E848, 0x16E73A], Level=[0x16E84A]} // Old Amber
StaticPokemon{}={Species=[0x16E7AF, 0x16E7B3, 0x16E7BE, 0x16E6BE], Level=[0x16E7C0]} // Helix Fossil
StaticPokemon{}={Species=[0x16E7F4, 0x16E7F8, 0x16E803, 0x16E6FC], Level=[0x16E805]} // Dome Fossil
StaticPokemon{}={Species=[0x161B32, 0x161B35, 0x161B74, 0x161BA7], Level=[0x161B37]} // Lapras in Silph. Co
StaticPokemon{}={Species=[0x16F817, 0x16F81A, 0x16F8D9], Level=[0x16F81C]} // Magikarp in Mt.Moon Center
StaticPokemon{}={Species=[0x16CC6C, 0x16CCE8], Level=[0x16CD2B]} // Abra
StaticPokemon{}={Species=[0x16CC7C, 0x16CCF3], Level=[0x16CD40]} // Clefairy
StaticPokemon{}={Species=[0x16CC8C, 0x16CD1F], Level=[0x16CD94]} // Pinsir
StaticPokemon{}={Species=[0x16CC9C, 0x16CCFE], Level=[0x16CD55]} // Dratini
StaticPokemon{}={Species=[0x16CCAC, 0x16CD14], Level=[0x16CD7F]} // Porygon
RoamingPokemon{}={Species=[0xA80224, 0x463D8C], Level=[0x141D18, 0x141D2C]} // Raikou
RoamingPokemon{}={Species=[0xA8021C, 0x463D84], Level=[0x141D18, 0x141D2C]} // Entei
RoamingPokemon{}={Species=[0xA80220, 0x463D88], Level=[0x141D18, 0x141D2C]} // Suicune
RoamingPokemonTweak=hardcoded_statics/roamers/lg_roamers_11
FossilLevelOffsets=[0x16E84A, 0x16E7C0, 0x16E805]
GhostMarowakTweak=hardcoded_statics/lg_marowak_11
GhostMarowakSpeciesOffsets=[0xA80024, 0x163526, 0x163551]
GhostMarowakLevelOffsets=[0xA80012, 0x163528]
GhostMarowakGenderOffset=0xA80004
SpecialMusicStatics=[144,145,146,150,249,250,386]
NewIndexToMusicTweak=musicfix/lg_musicfix_11
NewIndexToMusicPoolOffset=0xA80140
ShopItemOffsets=[0x164A0C, 0x167738, 0x167750, 0x16776C, 0x16778C, 0x16A2EC, 0x16A75C, 0x16AD2C, 0x16B3E4, 0x16B6E0, 0x16BB8C, 0x16BBC8, 0x16BC84, 0x16BCD8, 0x16BD10, 0x16D56C, 0x16EA9C, 0x16EB48, 0x16F030, 0x170BAC, 0x171908, 0x171D28, 0x171EE0]
CRC32=DAFFECEC

[Ruby (F)]
Game=AXVF
Version=0
Type=Ruby
CopyFrom=Ruby (U)
PokemonStats=0x207064
PokemonMovesets=0x210014
EggMoves=0x211628
PokemonTMHMCompat=0x20553C
PokemonEvolutions=0x20BFB4
StarterPokemon=0x3FF3F4
StarterItems=0x826C6
TrainerData=0x1F8904
TrainerClassNames=0x1F8610
ItemData=0x3CCFC4
MoveData=0x203578
MoveDescriptions=0x3C8434
MoveNames=0x200728
AbilityNames=0x202694
TmMoves=0x37D168
PokemonFrontSprites=0x1F075C
PokemonNormalPalettes=0x1F29BC
IntroCryOffset=0xA6DA
IntroSpriteOffset=0xB48C
IntroPaletteOffset=0xB498
IntroOtherOffset=0xB45A
TradeTableOffset=0x21DF10
RunIndoorsTweakOffset=0xE6220
CatchingTutorialOpponentMonOffset=0x8201C
CatchingTutorialPlayerMonOffset=0x10FB2A
PCPotionOffset=0x40DC10
TypeEffectivenessOffset=0x201B28
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x157B23, 0x157B51], Level=[0x157B53]} // Lileep
StaticPokemon{}={Species=[0x157B76, 0x157BA4], Level=[0x157BA6]} // Anorith
StaticPokemon{}={Species=[0x1A58A8, 0x15E2E6, 0x15E2ED], Level=[0x15E2E8]} // Groudon
StaticPokemon{}={Species=[0x15D049, 0x15D052], Level=[0x15D054]} // Regirock
StaticPokemon{}={Species=[0x15F461, 0x15F46A], Level=[0x15F46C]} // Regice
StaticPokemon{}={Species=[0x15F514, 0x15F51D], Level=[0x15F51F]} // Registeel
StaticPokemon{}={Species=[0x16108E, 0x1610B4], Level=[0x1610B6]} // Latias (Southern Island)
StaticPokemon{}={Species=[0x15F7D9, 0x15F7E0], Level=[0x15F7DB]} // Rayquaza
StaticPokemon{}={Species=[0x1A59E7, 0x1A59F0], Level=[0x1A59F2]} // Kecleons on OW (7)
StaticPokemon{}={Species=[0x151DA8, 0x151DB1], Level=[0x151DB3]} // Kecleon w/ Steven
StaticPokemon{}={Species=[0x15EDC3, 0x15EDCA], Level=[0x15EDC5]} // Voltorb 1
StaticPokemon{}={Species=[0x15EDE1, 0x15EDE8], Level=[0x15EDE3]} // Voltorb 2
StaticPokemon{}={Species=[0x15EDFF, 0x15EE06], Level=[0x15EE01]} // Voltorb 3
StaticPokemon{}={Species=[0x1A5905, 0x1A590C], Level=[0x1A5907]} // Electrode 1
StaticPokemon{}={Species=[0x1A5923, 0x1A592A], Level=[0x1A5925]} // Electrode 2
StaticPokemon{}={Species=[0x14EC5A]} // Wynaut Egg
StaticPokemon{}={Species=[0x15AF6F, 0x15AF7F], Level=[0x15AF71]} // Beldum
StaticPokemon{}={Species=[0x164259], Level=[0x16425B]} // Castform
RoamingPokemon{}={Species=[0x110E88, 0x134780], Level=[0x1346E4, 0x1346F4]} // Latios
FindMapsWithMonFunctionStartOffset=0x110E08
CreateInitialRoamerMonFunctionStartOffset=0x1346C8
ShopItemOffsets=[0x14BF90, 0x14C374, 0x15345C, 0x153478, 0x153B00, 0x153DA4, 0x153E40, 0x154394, 0x155790, 0x1557BC, 0x1568E8, 0x157898, 0x1580C0, 0x1580E8, 0x158540, 0x15A404, 0x15A438, 0x15A468, 0x15A490, 0x15A4F0, 0x15A514, 0x15AE00, 0x15B6F4, 0x15BF80]
CRC32=690FD310

[Ruby (F) 1.1]
Game=AXVF
Version=1
Type=Ruby
CopyStaticPokemon=1
CopyFrom=Ruby (F)
CRC32=9F981F72

[Sapphire (F)]
Game=AXPF
Version=0
Type=Sapp
CopyFrom=Ruby (F)
PokemonStats=0x206FF4
PokemonMovesets=0x20FFA4
EggMoves=0x2115B8
PokemonTMHMCompat=0x2054CC
PokemonEvolutions=0x20BF44
StarterPokemon=0x3FEF24
TrainerData=0x1F8894
TrainerClassNames=0x1F85A0
ItemData=0x3CCAF4
MoveData=0x203508
MoveDescriptions=0x3C7F64
MoveNames=0x2006B8
AbilityNames=0x202624
TmMoves=0x37D0F8
PokemonFrontSprites=0x1F06EC
PokemonNormalPalettes=0x1F294C
TradeTableOffset=0x21DEA0
RunIndoorsTweakOffset=0xE6220
PCPotionOffset=0x40D740
TypeEffectivenessOffset=0x201AB8
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x157AB3, 0x157AE1], Level=[0x157AE3]} // Lileep
StaticPokemon{}={Species=[0x157B06, 0x157B34], Level=[0x157B36]} // Anorith
StaticPokemon{}={Species=[0x1A5838, 0x15E276, 0x15E27D], Level=[0x15E278]} // Kyogre
StaticPokemon{}={Species=[0x15CFD9, 0x15CFE2], Level=[0x15CFE4]} // Regirock
StaticPokemon{}={Species=[0x15F3F1, 0x15F3FA], Level=[0x15F3FC]} // Regice
StaticPokemon{}={Species=[0x15F4A4, 0x15F4AD], Level=[0x15F4AF]} // Registeel
StaticPokemon{}={Species=[0x16101E, 0x161044], Level=[0x161046]} // Latios (Southern Island)
StaticPokemon{}={Species=[0x15F769, 0x15F770], Level=[0x15F76B]} // Rayquaza
StaticPokemon{}={Species=[0x1A5977, 0x1A5980], Level=[0x1A5982]} // Kecleons on OW (7)
StaticPokemon{}={Species=[0x151D3C, 0x151D45], Level=[0x151D47]} // Kecleon w/ Steven
StaticPokemon{}={Species=[0x15ED53, 0x15ED5A], Level=[0x15ED55]} // Voltorb 1
StaticPokemon{}={Species=[0x15ED71, 0x15ED78], Level=[0x15ED73]} // Voltorb 2
StaticPokemon{}={Species=[0x15ED8F, 0x15ED96], Level=[0x15ED91]} // Voltorb 3
StaticPokemon{}={Species=[0x1A5895, 0x1A589C], Level=[0x1A5897]} // Electrode 1
StaticPokemon{}={Species=[0x1A58B3, 0x1A58BA], Level=[0x1A58B5]} // Electrode 2
StaticPokemon{}={Species=[0x14EBEE]} // Wynaut Egg
StaticPokemon{}={Species=[0x15AEFF, 0x15AF0F], Level=[0x15AF01]} // Beldum
StaticPokemon{}={Species=[0x1641E9], Level=[0x1641EB]} // Castform
RoamingPokemon{}={Species=[0x110E84, 0x134784], Level=[0x1346E2, 0x1346F2]} // Latias
ShopItemOffsets=[0x14BF90, 0x14C374, 0x1533EC, 0x153408, 0x153A90, 0x153D34, 0x153DD0, 0x154324, 0x155720, 0x15574C, 0x156878, 0x157828, 0x158050, 0x158078, 0x1584D0, 0x15A394, 0x15A3C8, 0x15A3F8, 0x15A420, 0x15A480, 0x15A4A4, 0x15AD90, 0x15B684, 0x15BF10]
CRC32=3581A05F

[Sapphire (F) 1.1]
Game=AXPF
Version=1
Type=Sapp
CopyStaticPokemon=1
CopyFrom=Sapphire (F)
CRC32=2AE49146

[Emerald (F)]
Game=BPEF
Version=0
Type=Em
CopyFrom=Emerald (U)
PokemonMovesets=0x330EEC
EggMoves=0x332948
PokemonTMHMCompat=0x326408
PokemonEvolutions=0x32CE8C
StarterPokemon=0x5B63E4
StarterItems=0xB118E
TrainerData=0x317B60
TrainerClassNames=0x317804
MossdeepStevenTeamOffset=0x5E1BCC
MoveDescriptions=0x620920
TmMoves=0x619F1C
TmMovesDuplicate=0x61A3C8
MoveTutorData=0x619394
ItemImages=0x618798
TmPals=[0xDB5F6C, 0xDB5ECC, 0xDB6124, 0xDB5F94, 0xDB60AC, 0xDB60FC, 0xDB5FE4, 0xDB60D4, 0xDB605C, 0xDB60D4, 0xDB600C, 0xDB5F1C, 0xDB5FE4, 0xDB6084, 0xDB5F44, 0xDB5FBC, 0xDB5EF4, 0xDB6034]
IntroCryOffset=0x30B0C
IntroSpriteOffset=0x31924
TradeTableOffset=0x340A54
RunIndoorsTweakOffset=0x119E2C
TextSpeedValuesOffset=0x61341C
CatchingTutorialOpponentMonOffset=0xB0884
CatchingTutorialPlayerMonOffset=0x1390B6
PCPotionOffset=0x5E43FC
TypeEffectivenessOffset=0x322818
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x2142C0, 0x2142E5, 0x2142E8, 0x21436A, 0x214378], Level=[0x2142EA]} // Lileep
StaticPokemon{}={Species=[0x2142D2, 0x214388, 0x21438B, 0x21440D, 0x21441B], Level=[0x21438D]} // Anorith
StaticPokemon{}={Species=[0x23FEE1, 0x23FEEF, 0x23FF44, 0x1E5FD2, 0x1E6052, 0x1E610E, 0x1E618E], Level=[0x23FEF1]} // Kyogre
StaticPokemon{}={Species=[0x23FFB2, 0x23FFC0, 0x240015, 0x1E6012, 0x1E614E], Level=[0x23FFC2]} // Groudon
StaticPokemon{}={Species=[0x231B1F, 0x231B28, 0x231B6E], Level=[0x231B2A]} // Regirock
StaticPokemon{}={Species=[0x23DC62, 0x23DC6B, 0x23DCB1], Level=[0x23DC6D]} // Regice
StaticPokemon{}={Species=[0x23DD64, 0x23DD6D, 0x23DDB3], Level=[0x23DD6F]} // Registeel
StaticPokemon{}={Species=[0x23E449, 0x23E452, 0x23E498, 0x23E4E7, 0x23E505, 0x1E62B0, 0x1E62CE, 0x1E6364, 0x1E6382], Level=[0x23E454]} // Rayquaza
StaticPokemon{}={Species=[0x277645, 0x27764E], Level=[0x277650]} // Kecleons on OW (7)
StaticPokemon{}={Species=[0x1F68F4, 0x1F68FD], Level=[0x1F68FF]} // Kecleon w/ Steven
StaticPokemon{}={Species=[0x23C389, 0x23C390], Level=[0x23C38B]} // Voltorb 1
StaticPokemon{}={Species=[0x23C3D6, 0x23C3DD], Level=[0x23C3D8]} // Voltorb 2
StaticPokemon{}={Species=[0x23C423, 0x23C42A], Level=[0x23C425]} // Voltorb 3
StaticPokemon{}={Species=[0x23827E, 0x238285], Level=[0x238280]} // Electrode 1
StaticPokemon{}={Species=[0x2382CB, 0x2382D2], Level=[0x2382CD]} // Electrode 2
StaticPokemon{}={Species=[0x247FC7, 0x247FD5], Level=[0x247FD7]} // Sudowoodo in Battle Frontier
StaticPokemon{}={Species=[0x247D3E, 0x247E53], Level=[0x247E58]} // Latios on Southern Island
StaticPokemon{}={Species=[0x247D49, 0x247E66], Level=[0x247E6B]} // Latias on Southern Island
StaticPokemon{}={Species=[0x26CE61, 0x26CE71, 0x26CEBB, 0x26CEC6], Level=[0x26CE76]} // Deoxys on Birth Island
StaticPokemon{}={Species=[0x26CC78, 0x26CCB2, 0x26CD07, 0x26CD12], Level=[0x26CCB7]} // Mew on Faraway Island
StaticPokemon{}={Species=[0x26E00D, 0x26E03C, 0x26E08B, 0x26E096], Level=[0x26E041]} // Ho-Oh on Navel Rock
StaticPokemon{}={Species=[0x26E155, 0x26E160, 0x26E1AA, 0x26E1B5], Level=[0x26E165]} // Lugia on Navel Rock
StaticPokemon{}={Species=[0x1EB1A5]} // Wynaut Egg
StaticPokemon{}={Species=[0x22607E, 0x226081, 0x226103, 0x226114], Level=[0x226083]} // Beldum
StaticPokemon{}={Species=[0x275296, 0x275299, 0x275325], Level=[0x27529B]} // Castform
RoamingPokemon{}={Species=[0x16187C], Level=[0x1618AE, 0x1618BA]} // Latios
RoamingPokemon{}={Species=[0x161884], Level=[0x1618AE, 0x1618BA]} // Latias
CreateInitialRoamerMonFunctionStartOffset=0x161860
ShopItemOffsets=[0x1DCB78, 0x1DCF94, 0x1FD998, 0x1FD9B4, 0x1FFEC0, 0x201584, 0x2018D8, 0x2043A4, 0x209E84, 0x209EB0, 0x210184, 0x2138EC, 0x217B24, 0x217B4C, 0x21A544, 0x223138, 0x22316C, 0x223278, 0x2232A0, 0x223458, 0x22347C, 0x225B5C, 0x22A368, 0x22D460, 0x26C96C, 0x26D264, 0x26D290]
CRC32=A3FDCCB1

[Ruby (G)]
Game=AXVD
Version=0
Type=Ruby
CopyFrom=Ruby (U)
PokemonStats=0x20BBE8
PokemonMovesets=0x214B98
EggMoves=0x2161AC
PokemonTMHMCompat=0x20A0C0
PokemonEvolutions=0x210B38
StarterPokemon=0x403BF0
StarterItems=0x825DE
TrainerData=0x1FD478
TrainerClassNames=0x1FD184
ItemData=0x3D13DC
MoveData=0x2080FC
MoveDescriptions=0x3CC978
MoveNames=0x20529C
AbilityNames=0x207218
TmMoves=0x381CEC
PokemonFrontSprites=0x1F52D0
PokemonNormalPalettes=0x1F7530
IntroCryOffset=0xA6DA
IntroSpriteOffset=0xB48C
IntroPaletteOffset=0xB498
IntroOtherOffset=0xB45A
TradeTableOffset=0x222A94
RunIndoorsTweakOffset=0xE613C
CatchingTutorialOpponentMonOffset=0x81F34
CatchingTutorialPlayerMonOffset=0x10FA22
PCPotionOffset=0x412770
TypeEffectivenessOffset=0x20669C
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x157A3B, 0x157A69], Level=[0x157A6B]} // Lileep
StaticPokemon{}={Species=[0x157A8E, 0x157ABC], Level=[0x157ABE]} // Anorith
StaticPokemon{}={Species=[0x1A8695, 0x15E1FE, 0x15E205], Level=[0x15E200]} // Groudon
StaticPokemon{}={Species=[0x15CF61, 0x15CF6A], Level=[0x15CF6C]} // Regirock
StaticPokemon{}={Species=[0x15F379, 0x15F382], Level=[0x15F384]} // Regice
StaticPokemon{}={Species=[0x15F42C, 0x15F435], Level=[0x15F437]} // Registeel
StaticPokemon{}={Species=[0x160FA6, 0x160FCC], Level=[0x160FCE]} // Latias (Southern Island)
StaticPokemon{}={Species=[0x15F6F1, 0x15F6F8], Level=[0x15F6F3]} // Rayquaza
StaticPokemon{}={Species=[0x1A87D4, 0x1A87DD], Level=[0x1A87DF]} // Kecleons on OW (7)
StaticPokemon{}={Species=[0x151CC0, 0x151CC9], Level=[0x151CCB]} // Kecleon w/ Steven
StaticPokemon{}={Species=[0x15ECDB, 0x15ECE2], Level=[0x15ECDD]} // Voltorb 1
StaticPokemon{}={Species=[0x15ECF9, 0x15ED00], Level=[0x15ECFB]} // Voltorb 2
StaticPokemon{}={Species=[0x15ED17, 0x15ED1E], Level=[0x15ED19]} // Voltorb 3
StaticPokemon{}={Species=[0x1A86F2, 0x1A86F9], Level=[0x1A86F4]} // Electrode 1
StaticPokemon{}={Species=[0x1A8710, 0x1A8717], Level=[0x1A8712]} // Electrode 2
StaticPokemon{}={Species=[0x14EB72]} // Wynaut Egg
StaticPokemon{}={Species=[0x15AE87, 0x15AE97], Level=[0x15AE89]} // Beldum
StaticPokemon{}={Species=[0x164171], Level=[0x164173]} // Castform
RoamingPokemon{}={Species=[0x110D80, 0x134674], Level=[0x1345D8, 0x1345E8]} // Latios
FindMapsWithMonFunctionStartOffset=0x110D00
CreateInitialRoamerMonFunctionStartOffset=0x1345BC
ShopItemOffsets=[0x14BEA8, 0x14C28C, 0x153374, 0x153390, 0x153A18, 0x153CBC, 0x153D58, 0x1542AC, 0x1556A8, 0x1556D4, 0x156800, 0x1577B0, 0x157FD8, 0x158000, 0x158458, 0x15A31C, 0x15A350, 0x15A380, 0x15A3A8, 0x15A408, 0x15A42C, 0x15AD18, 0x15B60C, 0x15BE98]
CRC32=15E1E280

[Ruby (G) 1.1]
Game=AXVD
Version=1
Type=Ruby
CopyStaticPokemon=1
CopyFrom=Ruby (G)
CRC32=CAE89464

[Sapphire (G)]
Game=AXPD
Version=0
Type=Sapp
CopyFrom=Ruby (G)
PokemonStats=0x20BB7C
PokemonMovesets=0x214B2C
EggMoves=0x216140
PokemonTMHMCompat=0x20A054
PokemonEvolutions=0x210ACC
StarterPokemon=0x403B5C
TrainerData=0x1FD40C
TrainerClassNames=0x1FD118
ItemData=0x3D1348
MoveData=0x208090
MoveDescriptions=0x3CC8E4
MoveNames=0x20523D
AbilityNames=0x2071AC
TmMoves=0x381C80
PokemonFrontSprites=0x1F5264
PokemonNormalPalettes=0x1F74C4
TradeTableOffset=0x222A28
RunIndoorsTweakOffset=0xE613C
PCPotionOffset=0x4126DC
TypeEffectivenessOffset=0x206630
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x1579CF, 0x1579FD], Level=[0x1579FF]} // Lileep
StaticPokemon{}={Species=[0x157A22, 0x157A50], Level=[0x157A52]} // Anorith
StaticPokemon{}={Species=[0x1A8629, 0x15E192, 0x15E199], Level=[0x15E194]} // Kyogre
StaticPokemon{}={Species=[0x15CEF5, 0x15CEFE], Level=[0x15CF00]} // Regirock
StaticPokemon{}={Species=[0x15F30D, 0x15F316], Level=[0x15F318]} // Regice
StaticPokemon{}={Species=[0x15F3C0, 0x15F3C9], Level=[0x15F3CB]} // Registeel
StaticPokemon{}={Species=[0x160F3A, 0x160F60], Level=[0x160F62]} // Latios (Southern Island)
StaticPokemon{}={Species=[0x15F685, 0x15F68C], Level=[0x15F687]} // Rayquaza
StaticPokemon{}={Species=[0x1A8768, 0x1A8771], Level=[0x1A8773]} // Kecleons on OW (7)
StaticPokemon{}={Species=[0x151C58, 0x151C61], Level=[0x151C63]} // Kecleon w/ Steven
StaticPokemon{}={Species=[0x15EC6F, 0x15EC76], Level=[0x15EC71]} // Voltorb 1
StaticPokemon{}={Species=[0x15EC8D, 0x15EC94], Level=[0x15EC8F]} // Voltorb 2
StaticPokemon{}={Species=[0x15ECAB, 0x15ECB2], Level=[0x15ECAD]} // Voltorb 3
StaticPokemon{}={Species=[0x1A8686, 0x1A868D], Level=[0x1A8688]} // Electrode 1
StaticPokemon{}={Species=[0x1A86A4, 0x1A86AB], Level=[0x1A86A6]} // Electrode 2
StaticPokemon{}={Species=[0x14EB0A]} // Wynaut Egg
StaticPokemon{}={Species=[0x15AE1B, 0x15AE2B], Level=[0x15AE1D]} // Beldum
StaticPokemon{}={Species=[0x164105], Level=[0x164107]} // Castform
RoamingPokemon{}={Species=[0x110D7C, 0x13467C], Level=[0x1345DA, 0x1345EA]} // Latias
ShopItemOffsets=[0x14BEAC, 0x14C290, 0x153308, 0x153324, 0x1539AC, 0x153C50, 0x153CEC, 0x154240, 0x15563C, 0x155668, 0x156794, 0x157744, 0x157F6C, 0x157F94, 0x1583EC, 0x15A2B0, 0x15A2E4, 0x15A314, 0x15A33C, 0x15A39C, 0x15A3C0, 0x15ACAC, 0x15B5A0, 0x15BE2C]
CRC32=6FCD7A98

[Sapphire (G) 1.1]
Game=AXPD
Version=1
Type=Sapp
CopyStaticPokemon=1
CopyFrom=Sapphire (G)
CRC32=B0C40C7C

[Emerald (G)]
Game=BPED
Version=0
Type=Em
CopyFrom=Emerald (U)
PokemonMovesets=0x33DD3C
EggMoves=0x33F798
PokemonTMHMCompat=0x333258
PokemonEvolutions=0x339CDC
StarterPokemon=0x5C2FB0
StarterItems=0xB1196
TrainerData=0x3249A0
TrainerClassNames=0x324644
MossdeepStevenTeamOffset=0x5EEA98
MoveDescriptions=0x62DA80
TmMoves=0x62705C
TmMovesDuplicate=0x627508
MoveTutorData=0x6264D4
ItemImages=0x6258D8
TmPals=[0xDB5FA4, 0xDB5F04, 0xDB615C, 0xDB5FCC, 0xDB60E4, 0xDB6134, 0xDB601C, 0xDB610C, 0xDB6094, 0xDB610C, 0xDB6044, 0xDB5F54, 0xDB601C, 0xDB60BC, 0xDB5F7C, 0xDB5FF4, 0xDB5F2C, 0xDB606C]
IntroCryOffset=0x30B10
IntroSpriteOffset=0x31928
TradeTableOffset=0x34D89C
RunIndoorsTweakOffset=0x119E0C
TextSpeedValuesOffset=0x62055C
CatchingTutorialOpponentMonOffset=0xB088C
CatchingTutorialPlayerMonOffset=0x139096
PCPotionOffset=0x5F12C8
TypeEffectivenessOffset=0x32F658
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x215CD3, 0x215CF8, 0x215CFB, 0x215D7D, 0x215D8B], Level=[0x215CFD]} // Lileep
StaticPokemon{}={Species=[0x215CE5, 0x215D9B, 0x215D9E, 0x215E20, 0x215E2E], Level=[0x215DA0]} // Anorith
StaticPokemon{}={Species=[0x24354E, 0x24355C, 0x2435B1, 0x1E6397, 0x1E6417, 0x1E64D3, 0x1E6553], Level=[0x24355E]} // Kyogre
StaticPokemon{}={Species=[0x24361F, 0x24362D, 0x243682, 0x1E63D7, 0x1E6513], Level=[0x24362F]} // Groudon
StaticPokemon{}={Species=[0x234815, 0x23481E, 0x234864], Level=[0x234820]} // Regirock
StaticPokemon{}={Species=[0x2411BD, 0x2411C6, 0x24120C], Level=[0x2411C8]} // Regice
StaticPokemon{}={Species=[0x2412BF, 0x2412C8, 0x24130E], Level=[0x2412CA]} // Registeel
StaticPokemon{}={Species=[0x2419BC, 0x2419C5, 0x241A0B, 0x241A5A, 0x241A78, 0x1E6675, 0x1E6693, 0x1E6729, 0x1E6747], Level=[0x2419C7]} // Rayquaza
StaticPokemon{}={Species=[0x27DA85, 0x27DA8E], Level=[0x27DA90]} // Kecleons on OW (7)
StaticPokemon{}={Species=[0x1F7227, 0x1F7230], Level=[0x1F7232]} // Kecleon w/ Steven
StaticPokemon{}={Species=[0x23F82B, 0x23F832], Level=[0x23F82D]} // Voltorb 1
StaticPokemon{}={Species=[0x23F878, 0x23F87F], Level=[0x23F87A]} // Voltorb 2
StaticPokemon{}={Species=[0x23F8C5, 0x23F8CC], Level=[0x23F8C7]} // Voltorb 3
StaticPokemon{}={Species=[0x23B4C0, 0x23B4C7], Level=[0x23B4C2]} // Electrode 1
StaticPokemon{}={Species=[0x23B50D, 0x23B514], Level=[0x23B50F]} // Electrode 2
StaticPokemon{}={Species=[0x24B918, 0x24B926], Level=[0x24B928]} // Sudowoodo in Battle Frontier
StaticPokemon{}={Species=[0x24B68F, 0x24B7A4], Level=[0x24B7A9]} // Latios on Southern Island
StaticPokemon{}={Species=[0x24B69A, 0x24B7B7], Level=[0x24B7BC]} // Latias on Southern Island
StaticPokemon{}={Species=[0x272CA4, 0x272CB4, 0x272CFE, 0x272D09], Level=[0x272CB9]} // Deoxys on Birth Island
StaticPokemon{}={Species=[0x272AB8, 0x272AF2, 0x272B47, 0x272B52], Level=[0x272AF7]} // Mew on Faraway Island
StaticPokemon{}={Species=[0x273F63, 0x273F92, 0x273FE1, 0x273FEC], Level=[0x273F97]} // Ho-Oh on Navel Rock
StaticPokemon{}={Species=[0x2740AB, 0x2740B6, 0x274100, 0x27410B], Level=[0x2740BB]} // Lugia on Navel Rock
StaticPokemon{}={Species=[0x1EB5E8]} // Wynaut Egg
StaticPokemon{}={Species=[0x228813, 0x228816, 0x228898, 0x2288A9], Level=[0x228818]} // Beldum
StaticPokemon{}={Species=[0x27B588, 0x27B58B, 0x27B617], Level=[0x27B58D]} // Castform
RoamingPokemon{}={Species=[0x161754], Level=[0x161786, 0x161792]} // Latios
RoamingPokemon{}={Species=[0x16175C], Level=[0x161786, 0x161792]} // Latias
CreateInitialRoamerMonFunctionStartOffset=0x161738
ShopItemOffsets=[0x1DCAB0, 0x1DCECC, 0x1FE648, 0x1FE664, 0x200C6C, 0x2022FC, 0x202648, 0x20516C, 0x20B150, 0x20B17C, 0x2118E4, 0x2152C0, 0x219878, 0x2198A0, 0x21C4C4, 0x22562C, 0x225660, 0x225770, 0x225798, 0x225954, 0x225978, 0x22830C, 0x22CC54, 0x22FE60, 0x272774, 0x2730A4, 0x2730D0]
CRC32=34C9DF89

[Ruby (S)]
Game=AXVS
Version=0
Type=Ruby
CopyFrom=Ruby (U)
PokemonStats=0x203994
PokemonMovesets=0x20C944
EggMoves=0x20DF58
PokemonTMHMCompat=0x201E6C
PokemonEvolutions=0x2088E4
StarterPokemon=0x3FB50C
StarterItems=0x82666
TrainerData=0x1F521C
TrainerClassNames=0x1F4F28
ItemData=0x3C8FFC
MoveData=0x1FFEA8
MoveDescriptions=0x3C4504
MoveNames=0x1FD040
AbilityNames=0x1FEFC4
TmMoves=0x379A9C
PokemonFrontSprites=0x1ED074
PokemonNormalPalettes=0x1EF2D4
IntroCryOffset=0xA6D2
IntroSpriteOffset=0xB484
IntroPaletteOffset=0xB490
IntroOtherOffset=0xB452
TradeTableOffset=0x21A840
RunIndoorsTweakOffset=0xE620C
CatchingTutorialOpponentMonOffset=0x81FBC
CatchingTutorialPlayerMonOffset=0x10FB26
PCPotionOffset=0x409FE0
TypeEffectivenessOffset=0x1FE440
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x157B5F, 0x157B8D], Level=[0x157B8F]} // Lileep
StaticPokemon{}={Species=[0x157BB2, 0x157BE0], Level=[0x157BE2]} // Anorith
StaticPokemon{}={Species=[0x1A376D, 0x15E322, 0x15E329], Level=[0x15E324]} // Groudon
StaticPokemon{}={Species=[0x15D085, 0x15D08E], Level=[0x15D090]} // Regirock
StaticPokemon{}={Species=[0x15F49D, 0x15F4A6], Level=[0x15F4A8]} // Regice
StaticPokemon{}={Species=[0x15F550, 0x15F559], Level=[0x15F55B]} // Registeel
StaticPokemon{}={Species=[0x1610CA, 0x1610F0], Level=[0x1610F2]} // Latias (Southern Island)
StaticPokemon{}={Species=[0x15F815, 0x15F81C], Level=[0x15F817]} // Rayquaza
StaticPokemon{}={Species=[0x1A38AC, 0x1A38B5], Level=[0x1A38B7]} // Kecleons on OW (7)
StaticPokemon{}={Species=[0x151DE4, 0x151DED], Level=[0x151DEF]} // Kecleon w/ Steven
StaticPokemon{}={Species=[0x15EDFF, 0x15EE06], Level=[0x15EE01]} // Voltorb 1
StaticPokemon{}={Species=[0x15EE1D, 0x15EE24], Level=[0x15EE1F]} // Voltorb 2
StaticPokemon{}={Species=[0x15EE3B, 0x15EE42], Level=[0x15EE3D]} // Voltorb 3
StaticPokemon{}={Species=[0x1A37CA, 0x1A37D1], Level=[0x1A37CC]} // Electrode 1
StaticPokemon{}={Species=[0x1A37E8, 0x1A37EF], Level=[0x1A37EA]} // Electrode 2
StaticPokemon{}={Species=[0x14EC96]} // Wynaut Egg
StaticPokemon{}={Species=[0x15AFAB, 0x15AFBB], Level=[0x15AFAD]} // Beldum
StaticPokemon{}={Species=[0x164295], Level=[0x164297]} // Castform
RoamingPokemon{}={Species=[0x110E80, 0x1347BC], Level=[0x134720, 0x134730]} // Latios
FindMapsWithMonFunctionStartOffset=0x110E00
CreateInitialRoamerMonFunctionStartOffset=0x134704
ShopItemOffsets=[0x14BFCC, 0x14C3B0, 0x153498, 0x1534B4, 0x153B3C, 0x153DE0, 0x153E7C, 0x1543D0, 0x1557CC, 0x1557F8, 0x156924, 0x1578D4, 0x1580FC, 0x158124, 0x15857C, 0x15A440, 0x15A474, 0x15A4A4, 0x15A4CC, 0x15A52C, 0x15A550, 0x15AE3C, 0x15B730, 0x15BFBC]
CRC32=EB0729CF

[Ruby (S) 1.1]
Game=AXVS
Version=1
Type=Ruby
CopyStaticPokemon=1
CopyFrom=Ruby (S)
CRC32=B980FFF5

[Sapphire (S)]
Game=AXPS
Version=0
Type=Sapp
CopyFrom=Ruby (S)
PokemonStats=0x203924
PokemonMovesets=0x20C8D4
EggMoves=0x20DEE8
PokemonTMHMCompat=0x201DFC
PokemonEvolutions=0x208874
StarterPokemon=0x3FB248
TrainerData=0x1F51AC
TrainerClassNames=0x1F4EB8
ItemData=0x3C8D38
MoveData=0x1FFE38
MoveDescriptions=0x3C4240
MoveNames=0x1FCFD0
AbilityNames=0x1FEF54
TmMoves=0x379A2C
PokemonFrontSprites=0x1ED004
PokemonNormalPalettes=0x1EF264
TradeTableOffset=0x21A7D0
RunIndoorsTweakOffset=0xE620C
PCPotionOffset=0x409D1C
TypeEffectivenessOffset=0x1FE3D0
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x157AEF, 0x157B1D], Level=[0x157B1F]} // Lileep
StaticPokemon{}={Species=[0x157B42, 0x157B70], Level=[0x157B72]} // Anorith
StaticPokemon{}={Species=[0x1A36FD, 0x15E2B2, 0x15E2B9], Level=[0x15E2B4]} // Kyogre
StaticPokemon{}={Species=[0x15D015, 0x15D01E], Level=[0x15D020]} // Regirock
StaticPokemon{}={Species=[0x15F42D, 0x15F436], Level=[0x15F438]} // Regice
StaticPokemon{}={Species=[0x15F4E0, 0x15F4E9], Level=[0x15F4EB]} // Registeel
StaticPokemon{}={Species=[0x16105A, 0x161080], Level=[0x161082]} // Latios (Southern Island)
StaticPokemon{}={Species=[0x15F7A5, 0x15F7AC], Level=[0x15F7A7]} // Rayquaza
StaticPokemon{}={Species=[0x1A383C, 0x1A3845], Level=[0x1A3847]} // Kecleons on OW (7)
StaticPokemon{}={Species=[0x151D78, 0x151D81], Level=[0x151D83]} // Kecleon w/ Steven
StaticPokemon{}={Species=[0x15ED8F, 0x15ED96], Level=[0x15ED91]} // Voltorb 1
StaticPokemon{}={Species=[0x15EDAD, 0x15EDB4], Level=[0x15EDAF]} // Voltorb 2
StaticPokemon{}={Species=[0x15EDCB, 0x15EDD2], Level=[0x15EDCD]} // Voltorb 3
StaticPokemon{}={Species=[0x1A375A, 0x1A3761], Level=[0x1A375C]} // Electrode 1
StaticPokemon{}={Species=[0x1A3778, 0x1A377F], Level=[0x1A377A]} // Electrode 2
StaticPokemon{}={Species=[0x14EC2A]} // Wynaut Egg
StaticPokemon{}={Species=[0x15AF3B, 0x15AF4B], Level=[0x15AF3D]} // Beldum
StaticPokemon{}={Species=[0x164225], Level=[0x164227]} // Castform
RoamingPokemon{}={Species=[0x110E7C, 0x1347C0], Level=[0x13471E, 0x13472E]} // Latias
ShopItemOffsets=[0x14BFCC, 0x14C3B0, 0x153428, 0x153444, 0x153ACC, 0x153D70, 0x153E0C, 0x154360, 0x15575C, 0x155788, 0x1568B4, 0x157864, 0x15808C, 0x1580B4, 0x15850C, 0x15A3D0, 0x15A404, 0x15A434, 0x15A45C, 0x15A4BC, 0x15A4E0, 0x15ADCC, 0x15B6C0, 0x15BF4C]
CRC32=A04F5F0B

[Sapphire (S) 1.1]
Game=AXPS
Version=1
Type=Sapp
CopyStaticPokemon=1
CopyFrom=Sapphire (S)
CRC32=F2C88931

[Emerald (S)]
Game=BPES
Version=0
Type=Em
CopyFrom=Emerald (U)
PokemonMovesets=0x32F638
EggMoves=0x331094
PokemonTMHMCompat=0x324B54
PokemonEvolutions=0x32B5D8
StarterPokemon=0x5B49FC
StarterItems=0xBE596
TrainerData=0x316294
TrainerClassNames=0x315F38
MossdeepStevenTeamOffset=0x5E04A4
MoveDescriptions=0x61EE38
TmMoves=0x6189D4
TmMovesDuplicate=0x618E80
MoveTutorData=0x617E4C
ItemImages=0x617250
TmPals=[0xDB5F00, 0xDB5E60, 0xDB60B8, 0xDB5F28, 0xDB6040, 0xDB6090, 0xDB5F78, 0xDB6068, 0xDB5FF0, 0xDB6068, 0xDB5FA0, 0xDB5EB0, 0xDB5F78, 0xDB6018, 0xDB5ED8, 0xDB5F50, 0xDB5E88, 0xDB5FC8]
IntroCryOffset=0x30B0C
IntroSpriteOffset=0x31924
TradeTableOffset=0x33F1AC
RunIndoorsTweakOffset=0x119E00
TextSpeedValuesOffset=0x611ED4
CatchingTutorialOpponentMonOffset=0xB0884
CatchingTutorialPlayerMonOffset=0x13908A
PCPotionOffset=0x5E2CD4
TypeEffectivenessOffset=0x320F4C
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x21307F, 0x2130A4, 0x2130A7, 0x213129, 0x213137], Level=[0x2130A9]} // Lileep
StaticPokemon{}={Species=[0x213091, 0x213147, 0x21314A, 0x2131CC, 0x2131DA], Level=[0x21314C]} // Anorith
StaticPokemon{}={Species=[0x23DCC5, 0x23DCD3, 0x23DD28, 0x1E5B08, 0x1E5B88, 0x1E5C44, 0x1E5CC4], Level=[0x23DCD5]} // Kyogre
StaticPokemon{}={Species=[0x23DD96, 0x23DDA4, 0x23DDF9, 0x1E5B48, 0x1E5C84], Level=[0x23DDA6]} // Groudon
StaticPokemon{}={Species=[0x22FD3F, 0x22FD48, 0x22FD8E], Level=[0x22FD4A]} // Regirock
StaticPokemon{}={Species=[0x23BB32, 0x23BB3B, 0x23BB81], Level=[0x23BB3D]} // Regice
StaticPokemon{}={Species=[0x23BC34, 0x23BC3D, 0x23BC83], Level=[0x23BC3F]} // Registeel
StaticPokemon{}={Species=[0x23C305, 0x23C30E, 0x23C354, 0x23C3A3, 0x23C3C1, 0x1E5DE6, 0x1E5E04, 0x1E5E9A, 0x1E5EB8], Level=[0x23C310]} // Rayquaza
StaticPokemon{}={Species=[0x276188, 0x276191], Level=[0x276193]} // Kecleons on OW (7)
StaticPokemon{}={Species=[0x1F5CDA, 0x1F5CE3], Level=[0x1F5CE5]} // Kecleon w/ Steven
StaticPokemon{}={Species=[0x23A38F, 0x23A396], Level=[0x23A391]} // Voltorb 1
StaticPokemon{}={Species=[0x23A3DC, 0x23A3E3], Level=[0x23A3DE]} // Voltorb 2
StaticPokemon{}={Species=[0x23A429, 0x23A430], Level=[0x23A42B]} // Voltorb 3
StaticPokemon{}={Species=[0x236418, 0x23641F], Level=[0x23641A]} // Electrode 1
StaticPokemon{}={Species=[0x236465, 0x23646C], Level=[0x236467]} // Electrode 2
StaticPokemon{}={Species=[0x245C8C, 0x245C9A], Level=[0x245C9C]} // Sudowoodo in Battle Frontier
StaticPokemon{}={Species=[0x245A03, 0x245B18], Level=[0x245B1D]} // Latios on Southern Island
StaticPokemon{}={Species=[0x245A0E, 0x245B2B], Level=[0x245B30]} // Latias on Southern Island
StaticPokemon{}={Species=[0x26B948, 0x26B958, 0x26B9A2, 0x26B9AD], Level=[0x26B95D]} // Deoxys on Birth Island
StaticPokemon{}={Species=[0x26B75A, 0x26B794, 0x26B7E9, 0x26B7F4], Level=[0x26B799]} // Mew on Faraway Island
StaticPokemon{}={Species=[0x26CBEA, 0x26CC19, 0x26CC68, 0x26CC73], Level=[0x26CC1E]} // Ho-Oh on Navel Rock
StaticPokemon{}={Species=[0x26CD32, 0x26CD3D, 0x26CD87, 0x26CD92], Level=[0x26CD42]} // Lugia on Navel Rock
StaticPokemon{}={Species=[0x1EA97A]} // Wynaut Egg
StaticPokemon{}={Species=[0x22477B, 0x22477E, 0x224800, 0x224811], Level=[0x224780]} // Beldum
StaticPokemon{}={Species=[0x273D60, 0x273D63, 0x273DEF], Level=[0x273D65]} // Castform
RoamingPokemon{}={Species=[0x161854], Level=[0x161886, 0x161892]} // Latios
RoamingPokemon{}={Species=[0x16185C], Level=[0x161886, 0x161892]} // Latias
CreateInitialRoamerMonFunctionStartOffset=0x161838
ShopItemOffsets=[0x1DCB78, 0x1DCF94, 0x1FCAA4, 0x1FCAC0, 0x1FEEEC, 0x2004F4, 0x200814, 0x203228, 0x208C38, 0x208C64, 0x20EF28, 0x2126F8, 0x21683C, 0x216864, 0x2190A4, 0x221898, 0x2218CC, 0x2219E0, 0x221A08, 0x221B9C, 0x221BC0, 0x2242AC, 0x228858, 0x22B7C8, 0x26B41C, 0x26BD48, 0x26BD74]
CRC32=8C4D3108

[Ruby (I)]
Game=AXVI
Version=0
Type=Ruby
CopyFrom=Ruby (U)
PokemonStats=0x2008F0
PokemonMovesets=0x2098A0
EggMoves=0x20AEB4
PokemonTMHMCompat=0x1FEDC8
PokemonEvolutions=0x205840
StarterPokemon=0x3F8714
StarterItems=0x8257E
TrainerData=0x1F2198
TrainerClassNames=0x1F1EA4
ItemData=0x3C5FF8
MoveData=0x1FCE04
MoveDescriptions=0x3C158C
MoveNames=0x1F9FBC
AbilityNames=0x1FBF20
TmMoves=0x3769F4
PokemonFrontSprites=0x1E9FF0
PokemonNormalPalettes=0x1EC250
IntroCryOffset=0xA6D2
IntroSpriteOffset=0xB484
IntroPaletteOffset=0xB490
IntroOtherOffset=0xB452
TradeTableOffset=0x21779C
RunIndoorsTweakOffset=0xE6118
CatchingTutorialOpponentMonOffset=0x81ED4
CatchingTutorialPlayerMonOffset=0x10FA22
PCPotionOffset=0x406FE0
TypeEffectivenessOffset=0x1FB3BC
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x157A87, 0x157AB5], Level=[0x157AB7]} // Lileep
StaticPokemon{}={Species=[0x157ADA, 0x157B08], Level=[0x157B0A]} // Anorith
StaticPokemon{}={Species=[0x1A0E4D, 0x15E24A, 0x15E251], Level=[0x15E24C]} // Groudon
StaticPokemon{}={Species=[0x15CFAD, 0x15CFB6], Level=[0x15CFB8]} // Regirock
StaticPokemon{}={Species=[0x15F3C5, 0x15F3CE], Level=[0x15F3D0]} // Regice
StaticPokemon{}={Species=[0x15F478, 0x15F481], Level=[0x15F483]} // Registeel
StaticPokemon{}={Species=[0x160FF2, 0x161018], Level=[0x16101A]} // Latias (Southern Island)
StaticPokemon{}={Species=[0x15F73D, 0x15F744], Level=[0x15F73F]} // Rayquaza
StaticPokemon{}={Species=[0x1A0F8C, 0x1A0F95], Level=[0x1A0F97]} // Kecleons on OW (7)
StaticPokemon{}={Species=[0x151D0C, 0x151D15], Level=[0x151D17]} // Kecleon w/ Steven
StaticPokemon{}={Species=[0x15ED27, 0x15ED2E], Level=[0x15ED29]} // Voltorb 1
StaticPokemon{}={Species=[0x15ED45, 0x15ED4C], Level=[0x15ED47]} // Voltorb 2
StaticPokemon{}={Species=[0x15ED63, 0x15ED6A], Level=[0x15ED65]} // Voltorb 3
StaticPokemon{}={Species=[0x1A0EAA, 0x1A0EB1], Level=[0x1A0EAC]} // Electrode 1
StaticPokemon{}={Species=[0x1A0EC8, 0x1A0ECF], Level=[0x1A0ECA]} // Electrode 2
StaticPokemon{}={Species=[0x14EBBE]} // Wynaut Egg
StaticPokemon{}={Species=[0x15AED3, 0x15AEE3], Level=[0x15AED5]} // Beldum
StaticPokemon{}={Species=[0x1641BD], Level=[0x1641BF]} // Castform
RoamingPokemon{}={Species=[0x110D80, 0x1346BC], Level=[0x134620, 0x134630]} // Latios
FindMapsWithMonFunctionStartOffset=0x110D00
CreateInitialRoamerMonFunctionStartOffset=0x134604
ShopItemOffsets=[0x14BEF4, 0x14C2D8, 0x1533C0, 0x1533DC, 0x153A64, 0x153D08, 0x153DA4, 0x1542F8, 0x1556F4, 0x155720, 0x15684C, 0x1577FC, 0x158024, 0x15804C, 0x1584A4, 0x15A368, 0x15A39C, 0x15A3CC, 0x15A3F4, 0x15A454, 0x15A478, 0x15AD64, 0x15B658, 0x15BEE4]
CRC32=C18231A9

[Ruby (I) 1.1]
Game=AXVI
Version=1
Type=Ruby
CopyStaticPokemon=1
CopyFrom=Ruby (I)
CRC32=9305E793

[Sapphire (I)]
Game=AXPI
Version=0
Type=Sapp
CopyFrom=Ruby (I)
PokemonStats=0x200880
PokemonMovesets=0x209830
EggMoves=0x20AE44
PokemonTMHMCompat=0x1FED58
PokemonEvolutions=0x2057D0
StarterPokemon=0x3F83B8
TrainerData=0x1F2128
TrainerClassNames=0x1F1E34
ItemData=0x3C5C9C
MoveData=0x1FCD94
MoveDescriptions=0x3C1230
MoveNames=0x1F9F4C
AbilityNames=0x1FBEB0
TmMoves=0x376984
PokemonFrontSprites=0x1E9F80
PokemonNormalPalettes=0x1EC1E0
TradeTableOffset=0x21772C
RunIndoorsTweakOffset=0xE6118
PCPotionOffset=0x406C84
TypeEffectivenessOffset=0x1FB34C
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x157A17, 0x157A45], Level=[0x157A47]} // Lileep
StaticPokemon{}={Species=[0x157A6A, 0x157A98], Level=[0x157A9A]} // Anorith
StaticPokemon{}={Species=[0x1A0DDD, 0x15E1DA, 0x15E1E1], Level=[0x15E1DC]} // Kyogre
StaticPokemon{}={Species=[0x15CF3D, 0x15CF46], Level=[0x15CF48]} // Regirock
StaticPokemon{}={Species=[0x15F355, 0x15F35E], Level=[0x15F360]} // Regice
StaticPokemon{}={Species=[0x15F408, 0x15F411], Level=[0x15F413]} // Registeel
StaticPokemon{}={Species=[0x160F82, 0x160FA8], Level=[0x160FAA]} // Latios (Southern Island)
StaticPokemon{}={Species=[0x15F6CD, 0x15F6D4], Level=[0x15F6CF]} // Rayquaza
StaticPokemon{}={Species=[0x1A0F1C, 0x1A0F25], Level=[0x1A0F27]} // Kecleons on OW (7)
StaticPokemon{}={Species=[0x151CA0, 0x151CA9], Level=[0x151CAB]} // Kecleon w/ Steven
StaticPokemon{}={Species=[0x15ECB7, 0x15ECBE], Level=[0x15ECB9]} // Voltorb 1
StaticPokemon{}={Species=[0x15ECD5, 0x15ECDC], Level=[0x15ECD7]} // Voltorb 2
StaticPokemon{}={Species=[0x15ECF3, 0x15ECFA], Level=[0x15ECF5]} // Voltorb 3
StaticPokemon{}={Species=[0x1A0E3A, 0x1A0E41], Level=[0x1A0E3C]} // Electrode 1
StaticPokemon{}={Species=[0x1A0E58, 0x1A0E5F], Level=[0x1A0E5A]} // Electrode 2
StaticPokemon{}={Species=[0x14EB52]} // Wynaut Egg
StaticPokemon{}={Species=[0x15AE63, 0x15AE73], Level=[0x15AE65]} // Beldum
StaticPokemon{}={Species=[0x16414D], Level=[0x16414F]} // Castform
RoamingPokemon{}={Species=[0x110D7C, 0x1346C0], Level=[0x13461E, 0x13462E]} // Latias
ShopItemOffsets=[0x14BEF4, 0x14C2D8, 0x153350, 0x15336C, 0x1539F4, 0x153C98, 0x153D34, 0x154288, 0x155684, 0x1556B0, 0x1567DC, 0x15778C, 0x157FB4, 0x157FDC, 0x158434, 0x15A2F8, 0x15A32C, 0x15A35C, 0x15A384, 0x15A3E4, 0x15A408, 0x15ACF4, 0x15B5E8, 0x15BE74]
CRC32=0C7992A9

[Sapphire (I) 1.1]
Game=AXPI
Version=1
Type=Sapp
CopyStaticPokemon=1
CopyFrom=Sapphire (I)
CRC32=5EFE4493

[Emerald (I)]
Game=BPEI
Version=0
Type=Em
CopyFrom=Emerald (U)
PokemonMovesets=0x328D7C
EggMoves=0x32A7D8
PokemonTMHMCompat=0x31E298
PokemonEvolutions=0x324D1C
StarterPokemon=0x5AE994
StarterItems=0xBE596
TrainerData=0x30F9F4
TrainerClassNames=0x30F698
MossdeepStevenTeamOffset=0x5DA218
MoveDescriptions=0x619038
TmMoves=0x612730
TmMovesDuplicate=0x612BDC
MoveTutorData=0x611BA8
ItemImages=0x610FAC
TmPals=[0xDB5FA4, 0xDB5F04, 0xDB615C, 0xDB5FCC, 0xDB60E4, 0xDB6134, 0xDB601C, 0xDB610C, 0xDB6094, 0xDB610C, 0xDB6044, 0xDB5F54, 0xDB601C, 0xDB60BC, 0xDB5F7C, 0xDB5FF4, 0xDB5F2C, 0xDB606C]
IntroCryOffset=0x30B10
IntroSpriteOffset=0x31928
TradeTableOffset=0x3388CC
RunIndoorsTweakOffset=0x119DF8
TextSpeedValuesOffset=0x60BC30
CatchingTutorialOpponentMonOffset=0xB0884
CatchingTutorialPlayerMonOffset=0x139082
PCPotionOffset=0x5DCA48
TypeEffectivenessOffset=0x31A6AC
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x211669, 0x21168E, 0x211691, 0x211713, 0x211721], Level=[0x211693]} // Lileep
StaticPokemon{}={Species=[0x21167B, 0x211731, 0x211734, 0x2117B6, 0x2117C4], Level=[0x211736]} // Anorith
StaticPokemon{}={Species=[0x23B0B6, 0x23B0C4, 0x23B119, 0x1E54AB, 0x1E552B, 0x1E55E7, 0x1E5667], Level=[0x23B0C6]} // Kyogre
StaticPokemon{}={Species=[0x23B187, 0x23B195, 0x23B1EA, 0x1E54EB, 0x1E5627], Level=[0x23B197]} // Groudon
StaticPokemon{}={Species=[0x22D8D2, 0x22D8DB, 0x22D921], Level=[0x22D8DD]} // Regirock
StaticPokemon{}={Species=[0x239092, 0x23909B, 0x2390E1], Level=[0x23909D]} // Regice
StaticPokemon{}={Species=[0x239194, 0x23919D, 0x2391E3], Level=[0x23919F]} // Registeel
StaticPokemon{}={Species=[0x23983F, 0x239848, 0x23988E, 0x2398DD, 0x2398FB, 0x1E5789, 0x1E57A7, 0x1E583D, 0x1E585B], Level=[0x23984A]} // Rayquaza
StaticPokemon{}={Species=[0x271FDB, 0x271FE4], Level=[0x271FE6]} // Kecleons on OW (7)
StaticPokemon{}={Species=[0x1F51E3, 0x1F51EC], Level=[0x1F51EE]} // Kecleon w/ Steven
StaticPokemon{}={Species=[0x23794B, 0x237952], Level=[0x23794D]} // Voltorb 1
StaticPokemon{}={Species=[0x237998, 0x23799F], Level=[0x23799A]} // Voltorb 2
StaticPokemon{}={Species=[0x2379E5, 0x2379EC], Level=[0x2379E7]} // Voltorb 3
StaticPokemon{}={Species=[0x233AB2, 0x233AB9], Level=[0x233AB4]} // Electrode 1
StaticPokemon{}={Species=[0x233AFF, 0x233B06], Level=[0x233B01]} // Electrode 2
StaticPokemon{}={Species=[0x242D25, 0x242D33], Level=[0x242D35]} // Sudowoodo in Battle Frontier
StaticPokemon{}={Species=[0x242A9C, 0x242BB1], Level=[0x242BB6]} // Latios on Southern Island
StaticPokemon{}={Species=[0x242AA7, 0x242BC4], Level=[0x242BC9]} // Latias on Southern Island
StaticPokemon{}={Species=[0x267A89, 0x267A99, 0x267AE3, 0x267AEE], Level=[0x267A9E]} // Deoxys on Birth Island
StaticPokemon{}={Species=[0x2678A1, 0x2678DB, 0x267930, 0x26793B], Level=[0x2678E0]} // Mew on Faraway Island
StaticPokemon{}={Species=[0x268C5D, 0x268C8C, 0x268CDB, 0x268CE6], Level=[0x268C91]} // Ho-Oh on Navel Rock
StaticPokemon{}={Species=[0x268DA5, 0x268DB0, 0x268DFA, 0x268E05], Level=[0x268DB5]} // Lugia on Navel Rock
StaticPokemon{}={Species=[0x1EA261]} // Wynaut Egg
StaticPokemon{}={Species=[0x222657, 0x22265A, 0x2226DC, 0x2226ED], Level=[0x22265C]} // Beldum
StaticPokemon{}={Species=[0x26FC1D, 0x26FC20, 0x26FCAC], Level=[0x26FC22]} // Castform
RoamingPokemon{}={Species=[0x161744], Level=[0x161776, 0x161782]} // Latios
RoamingPokemon{}={Species=[0x16174C], Level=[0x161776, 0x161782]} // Latias
CreateInitialRoamerMonFunctionStartOffset=0x161728
ShopItemOffsets=[0x1DC9DC, 0x1DCDF8, 0x1FBDF0, 0x1FBE0C, 0x1FE0B8, 0x1FF604, 0x1FF8EC, 0x2021FC, 0x207868, 0x207894, 0x20D808, 0x210D10, 0x214BCC, 0x214BF4, 0x2173A4, 0x21F918, 0x21F94C, 0x21FA34, 0x21FA5C, 0x21FC04, 0x21FC28, 0x2221B8, 0x2265DC, 0x229518, 0x267574, 0x267E8C, 0x267EB8]
CRC32=A0AEC80A

[Fire Red (F)]
Game=BPRF
Version=0
Type=FRLG
CopyFrom=Fire Red (U) 1.0
PokemonMovesets=0x257C04
EggMoves=0x25935C
PokemonTMHMCompat=0x24D018
PokemonEvolutions=0x253BA4
StarterPokemon=0x169BDC
TrainerData=0x238ED4
TrainerClassNames=0x238964
MoveDescriptions=0x47E7DC
TmMoves=0x453BA8
TmMovesDuplicate=0x453E10
MoveTutorData=0x453164
ItemImages=0x3CE114
TmPals=[0xE91DB8, 0xE91D18, 0xE91F70, 0xE91DE0, 0xE91EF8, 0xE91F48, 0xE91E30, 0xE91F20, 0xE91EA8, 0xE91F20, 0xE91E58, 0xE91D68, 0xE91E30, 0xE91ED0, 0xE91D90, 0xE91E08, 0xE91D40, 0xE91E80]
IntroCryOffset=0x12FC44
IntroSpriteOffset=0x1310AC
IntroOtherOffset=0x131058
TradeTableOffset=0x2673DC
RunIndoorsTweakOffset=0xBD640
TextSpeedValuesOffset=0x4178FC
CatchingTutorialOpponentMonOffset=0x7F8B0
PCPotionOffset=0x3FAC28
TypeEffectivenessOffset=0x24945C
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x16C49A, 0x16C49D, 0x16C4DD, 0x16C511], Level=[0x16C49F]} // Eevee in Celadon Condominiums
StaticPokemon{}={Species=[0x16EC34, 0x16EC3B], Level=[0x16ECAE]} // Hitmonlee in Fighting Dojo
StaticPokemon{}={Species=[0x16EC7A, 0x16EC81], Level=[0x16ECAE]} // Hitmonchan in Fighting Dojo
StaticPokemon{}={Species=[0x163862, 0x163869], Level=[0x163864]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x1638C0, 0x1638C7], Level=[0x1638C2]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x1637EE, 0x1637F5, 0x163849], Level=[0x1637F0]} // Zapdos in The Power Plant
StaticPokemon{}={Species=[0x1631E2, 0x1631E9, 0x16323D, 0x1700C8, 0x1700D4], Level=[0x1631E4]} // Articuno (Seafoam and Route 15 Gatehouse)
StaticPokemon{}={Species=[0x163B69, 0x163B70, 0x163BC4], Level=[0x163B6B]} // Moltres in Mt.Ember
StaticPokemon{}={Species=[0x16252C, 0x162540, 0x162586], Level=[0x162542]} // Mewtwo in Unk. Dungeon
StaticPokemon{}={Species=[0x16806D, 0x168073, 0x160CBE, 0x160CC4], Level=[0x16806F]} // Snorlax (Route 12 and S.S. Anne)
StaticPokemon{}={Species=[0x168179, 0x16817F], Level=[0x16817B]} // Snorlax (Route 16)
StaticPokemon{}={Species=[0x163CDE, 0x163CE4], Level=[0x163CE6]} // Hypno in Berry Forest
StaticPokemon{}={Species=[0x16530A, 0x16531A, 0x165364, 0x16536F], Level=[0x16531F]} // Deoxys on Birth Island
StaticPokemon{}={Species=[0x165060, 0x16508F, 0x1650DE, 0x1650E9], Level=[0x165094]} // Ho-Oh on Navel Rock
StaticPokemon{}={Species=[0x1651AE, 0x1651B9, 0x165203, 0x16520E], Level=[0x1651BE]} // Lugia on Navel Rock
StaticPokemon{}={Species=[0x16E80D, 0x16E811, 0x16E81C, 0x16E70E], Level=[0x16E81E]} // Old Amber
StaticPokemon{}={Species=[0x16E783, 0x16E787, 0x16E792, 0x16E692], Level=[0x16E794]} // Helix Fossil
StaticPokemon{}={Species=[0x16E7C8, 0x16E7CC, 0x16E7D7, 0x16E6D0], Level=[0x16E7D9]} // Dome Fossil
StaticPokemon{}={Species=[0x161AFA, 0x161AFD, 0x161B3F, 0x161B75], Level=[0x161AFF]} // Lapras in Silph. Co
StaticPokemon{}={Species=[0x16F7EB, 0x16F7EE, 0x16F8B0], Level=[0x16F7F0]} // Magikarp in Mt.Moon Center
StaticPokemon{}={Species=[0x16CC40, 0x16CCBC], Level=[0x16CCFF]} // Abra
StaticPokemon{}={Species=[0x16CC50, 0x16CCC7], Level=[0x16CD14]} // Clefairy
StaticPokemon{}={Species=[0x16CC70, 0x16CCDD], Level=[0x16CD3E]} // Scyther
StaticPokemon{}={Species=[0x16CC60, 0x16CCD2], Level=[0x16CD29]} // Dratini
StaticPokemon{}={Species=[0x16CC80, 0x16CCE8], Level=[0x16CD53]} // Porygon
FossilLevelOffsets=[0x16E81E, 0x16E794, 0x16E7D9]
ShopItemOffsets=[0x1649DC, 0x167708, 0x167720, 0x16773C, 0x16775C, 0x16A2C0, 0x16A730, 0x16AD00, 0x16B3B8, 0x16B6B4, 0x16BB60, 0x16BB9C, 0x16BC58, 0x16BCAC, 0x16BCE4, 0x16D540, 0x16EA70, 0x16EB1C, 0x16F004, 0x170B84, 0x1718E0, 0x171D00, 0x171EB8]
CRC32=5DC668F6

[Leaf Green (F)]
Game=BPGF
Version=0
Type=FRLG
CopyFrom=Fire Red (F)
PokemonMovesets=0x257BE4
EggMoves=0x25933C
PokemonTMHMCompat=0x24CFF4
PokemonEvolutions=0x253B84
StarterPokemon=0x169BB8
TrainerData=0x238EB0
TrainerClassNames=0x238940
MoveDescriptions=0x47D504
TmMoves=0x452968
TmMovesDuplicate=0x452BD0
MoveTutorData=0x451F24
ItemImages=0x3CDF50
TmPals=[0xE91E38, 0xE91D98, 0xE91FF0, 0xE91E60, 0xE91F78, 0xE91FC8, 0xE91EB0, 0xE91FA0, 0xE91F28, 0xE91FA0, 0xE91ED8, 0xE91DE8, 0xE91EB0, 0xE91F50, 0xE91E10, 0xE91E88, 0xE91DC0, 0xE91F00]
IntroCryOffset=0x12FC1C
IntroSpriteOffset=0x131084
IntroOtherOffset=0x131030
TradeTableOffset=0x2673BC
RunIndoorsTweakOffset=0xBD614
TextSpeedValuesOffset=0x417738
CatchingTutorialOpponentMonOffset=0x7F884
PCPotionOffset=0x3FAA64
TypeEffectivenessOffset=0x249438
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x16C476, 0x16C479, 0x16C4B9, 0x16C4ED], Level=[0x16C47B]} // Eevee in Celadon Condominiums
StaticPokemon{}={Species=[0x16EC10, 0x16EC17], Level=[0x16EC8A]} // Hitmonlee in Fighting Dojo
StaticPokemon{}={Species=[0x16EC56, 0x16EC5D], Level=[0x16EC8A]} // Hitmonchan in Fighting Dojo
StaticPokemon{}={Species=[0x16383E, 0x163845], Level=[0x163840]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x16389C, 0x1638A3], Level=[0x16389E]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x1637CA, 0x1637D1, 0x163825], Level=[0x1637CC]} // Zapdos in The Power Plant
StaticPokemon{}={Species=[0x1631BE, 0x1631C5, 0x163219, 0x1700A4, 0x1700B0], Level=[0x1631C0]} // Articuno (Seafoam and Route 15 Gatehouse)
StaticPokemon{}={Species=[0x163B45, 0x163B4C, 0x163BA0], Level=[0x163B47]} // Moltres in Mt.Ember
StaticPokemon{}={Species=[0x162508, 0x16251C, 0x162562], Level=[0x16251E]} // Mewtwo in Unk. Dungeon
StaticPokemon{}={Species=[0x168049, 0x16804F, 0x160C9A, 0x160CA0], Level=[0x16804B]} // Snorlax (Route 12 and S.S. Anne)
StaticPokemon{}={Species=[0x168155, 0x16815B], Level=[0x168157]} // Snorlax (Route 16)
StaticPokemon{}={Species=[0x163CBA, 0x163CC0], Level=[0x163CC2]} // Hypno in Berry Forest
StaticPokemon{}={Species=[0x1652E6, 0x1652F6, 0x165340, 0x16534B], Level=[0x1652FB]} // Deoxys on Birth Island
StaticPokemon{}={Species=[0x16503C, 0x16506B, 0x1650BA, 0x1650C5], Level=[0x165070]} // Ho-Oh on Navel Rock
StaticPokemon{}={Species=[0x16518A, 0x165195, 0x1651DF, 0x1651EA], Level=[0x16519A]} // Lugia on Navel Rock
StaticPokemon{}={Species=[0x16E7E9, 0x16E7ED, 0x16E7F8, 0x16E6EA], Level=[0x16E7FA]} // Old Amber
StaticPokemon{}={Species=[0x16E75F, 0x16E763, 0x16E76E, 0x16E66E], Level=[0x16E770]} // Helix Fossil
StaticPokemon{}={Species=[0x16E7A4, 0x16E7A8, 0x16E7B3, 0x16E6AC], Level=[0x16E7B5]} // Dome Fossil
StaticPokemon{}={Species=[0x161AD6, 0x161AD9, 0x161B1B, 0x161B51], Level=[0x161ADB]} // Lapras in Silph. Co
StaticPokemon{}={Species=[0x16F7C7, 0x16F7CA, 0x16F88C], Level=[0x16F7CC]} // Magikarp in Mt.Moon Center
StaticPokemon{}={Species=[0x16CC1C, 0x16CC98], Level=[0x16CCDB]} // Abra
StaticPokemon{}={Species=[0x16CC2C, 0x16CCA3], Level=[0x16CCF0]} // Clefairy
StaticPokemon{}={Species=[0x16CC3C, 0x16CCCF], Level=[0x16CD44]} // Pinsir
StaticPokemon{}={Species=[0x16CC4C, 0x16CCAE], Level=[0x16CD05]} // Dratini
StaticPokemon{}={Species=[0x16CC5C, 0x16CCC4], Level=[0x16CD2F]} // Porygon
FossilLevelOffsets=[0x16E7FA, 0x16E770, 0x16E7B5]
ShopItemOffsets=[0x1649B8, 0x1676E4, 0x1676FC, 0x167718, 0x167738, 0x16A29C, 0x16A70C, 0x16ACDC, 0x16B394, 0x16B690, 0x16BB3C, 0x16BB78, 0x16BC34, 0x16BC88, 0x16BCC0, 0x16D51C, 0x16EA4C, 0x16EAF8, 0x16EFE0, 0x170B60, 0x1718BC, 0x171CDC, 0x171E94]
CRC32=BA3285E3

[Fire Red (G)]
Game=BPRD
Version=0
Type=FRLG
CopyFrom=Fire Red (U) 1.0
PokemonMovesets=0x25D6D8
EggMoves=0x25EE30
PokemonTMHMCompat=0x252AEC
PokemonEvolutions=0x259678
StarterPokemon=0x169B20
TrainerData=0x23E998
TrainerClassNames=0x23E428
MoveDescriptions=0x486BEC
TmMoves=0x45B670
TmMovesDuplicate=0x45B8D8
MoveTutorData=0x45AC2C
ItemImages=0x3D3BE8
TmPals=[0xE91E84, 0xE91DE4, 0xE9203C, 0xE91EAC, 0xE91FC4, 0xE92014, 0xE91EFC, 0xE91FEC, 0xE91F74, 0xE91FEC, 0xE91F24, 0xE91E34, 0xE91EFC, 0xE91F9C, 0xE91E5C, 0xE91ED4, 0xE91E0C, 0xE91F4C]
IntroCryOffset=0x12FB88
IntroSpriteOffset=0x130FF0
IntroOtherOffset=0x130F9C
TradeTableOffset=0x26CEB0
RunIndoorsTweakOffset=0xBD580
TextSpeedValuesOffset=0x41F764
CatchingTutorialOpponentMonOffset=0x7F7F0
PCPotionOffset=0x402224
TypeEffectivenessOffset=0x24EF20
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x16C3DE, 0x16C3E1, 0x16C421, 0x16C455], Level=[0x16C3E3]} // Eevee in Celadon Condominiums
StaticPokemon{}={Species=[0x16EB78, 0x16EB7F], Level=[0x16EBF2]} // Hitmonlee in Fighting Dojo
StaticPokemon{}={Species=[0x16EBBE, 0x16EBC5], Level=[0x16EBF2]} // Hitmonchan in Fighting Dojo
StaticPokemon{}={Species=[0x1637A6, 0x1637AD], Level=[0x1637A8]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x163804, 0x16380B], Level=[0x163806]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x163732, 0x163739, 0x16378D], Level=[0x163734]} // Zapdos in The Power Plant
StaticPokemon{}={Species=[0x163126, 0x16312D, 0x163181, 0x17000C, 0x170018], Level=[0x163128]} // Articuno (Seafoam and Route 15 Gatehouse)
StaticPokemon{}={Species=[0x163AAD, 0x163AB4, 0x163B08], Level=[0x163AAF]} // Moltres in Mt.Ember
StaticPokemon{}={Species=[0x162470, 0x162484, 0x1624CA], Level=[0x162486]} // Mewtwo in Unk. Dungeon
StaticPokemon{}={Species=[0x167FB1, 0x167FB7, 0x160C02, 0x160C08], Level=[0x167FB3]} // Snorlax (Route 12 and S.S. Anne)
StaticPokemon{}={Species=[0x1680BD, 0x1680C3], Level=[0x1680BF]} // Snorlax (Route 16)
StaticPokemon{}={Species=[0x163C22, 0x163C28], Level=[0x163C2A]} // Hypno in Berry Forest
StaticPokemon{}={Species=[0x16524E, 0x16525E, 0x1652A8, 0x1652B3], Level=[0x165263]} // Deoxys on Birth Island
StaticPokemon{}={Species=[0x164FA4, 0x164FD3, 0x165022, 0x16502D], Level=[0x164FD8]} // Ho-Oh on Navel Rock
StaticPokemon{}={Species=[0x1650F2, 0x1650FD, 0x165147, 0x165152], Level=[0x165102]} // Lugia on Navel Rock
StaticPokemon{}={Species=[0x16E751, 0x16E755, 0x16E760, 0x16E652], Level=[0x16E762]} // Old Amber
StaticPokemon{}={Species=[0x16E6C7, 0x16E6CB, 0x16E6D6, 0x16E5D6], Level=[0x16E6D8]} // Helix Fossil
StaticPokemon{}={Species=[0x16E70C, 0x16E710, 0x16E71B, 0x16E614], Level=[0x16E71D]} // Dome Fossil
StaticPokemon{}={Species=[0x161A3E, 0x161A41, 0x161A83, 0x161AB9], Level=[0x161A43]} // Lapras in Silph. Co
StaticPokemon{}={Species=[0x16F72F, 0x16F732, 0x16F7F4], Level=[0x16F734]} // Magikarp in Mt.Moon Center
StaticPokemon{}={Species=[0x16CB84, 0x16CC00], Level=[0x16CC43]} // Abra
StaticPokemon{}={Species=[0x16CB94, 0x16CC0B], Level=[0x16CC58]} // Clefairy
StaticPokemon{}={Species=[0x16CBB4, 0x16CC21], Level=[0x16CC82]} // Scyther
StaticPokemon{}={Species=[0x16CBA4, 0x16CC16], Level=[0x16CC6D]} // Dratini
StaticPokemon{}={Species=[0x16CBC4, 0x16CC2C], Level=[0x16CC97]} // Porygon
FossilLevelOffsets=[0x16E762, 0x16E6D8, 0x16E71D]
ShopItemOffsets=[0x164920, 0x16764C, 0x167664, 0x167680, 0x1676A0, 0x16A204, 0x16A674, 0x16AC44, 0x16B2FC, 0x16B5F8, 0x16BAA4, 0x16BAE0, 0x16BB9C, 0x16BBF0, 0x16BC28, 0x16D484, 0x16E9B4, 0x16EA60, 0x16EF48, 0x170AC8, 0x171824, 0x171C44, 0x171DFC]
CRC32=1A81EEDF

[Leaf Green (G)]
Game=BPGD
Version=0
Type=FRLG
CopyFrom=Fire Red (G)
PokemonMovesets=0x25D6B8
EggMoves=0x25EE10
PokemonTMHMCompat=0x252AC8
PokemonEvolutions=0x259658
StarterPokemon=0x169AFC
TrainerData=0x23E974
TrainerClassNames=0x23E404
MoveDescriptions=0x485D58
TmMoves=0x45A874
TmMovesDuplicate=0x45AADC
MoveTutorData=0x459E30
ItemImages=0x3D3A24
TmPals=[0xE91F04, 0xE91E64, 0xE920BC, 0xE91F2C, 0xE92044, 0xE92094, 0xE91F7C, 0xE9206C, 0xE91FF4, 0xE9206C, 0xE91FA4, 0xE91EB4, 0xE91F7C, 0xE9201C, 0xE91EDC, 0xE91F54, 0xE91E8C, 0xE91FCC]
IntroCryOffset=0x12FB60
IntroSpriteOffset=0x130FC8
IntroOtherOffset=0x130F74
TradeTableOffset=0x26CE90
RunIndoorsTweakOffset=0xBD554
TextSpeedValuesOffset=0x41F5A0
CatchingTutorialOpponentMonOffset=0x7F7C4
PCPotionOffset=0x402060
TypeEffectivenessOffset=0x24EEFC
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x16C3BA, 0x16C3BD, 0x16C3FD, 0x16C431], Level=[0x16C3BF]} // Eevee in Celadon Condominiums
StaticPokemon{}={Species=[0x16EB54, 0x16EB5B], Level=[0x16EBCE]} // Hitmonlee in Fighting Dojo
StaticPokemon{}={Species=[0x16EB9A, 0x16EBA1], Level=[0x16EBCE]} // Hitmonchan in Fighting Dojo
StaticPokemon{}={Species=[0x163782, 0x163789], Level=[0x163784]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x1637E0, 0x1637E7], Level=[0x1637E2]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x16370E, 0x163715, 0x163769], Level=[0x163710]} // Zapdos in The Power Plant
StaticPokemon{}={Species=[0x163102, 0x163109, 0x16315D, 0x16FFE8, 0x16FFF4], Level=[0x163104]} // Articuno (Seafoam and Route 15 Gatehouse)
StaticPokemon{}={Species=[0x163A89, 0x163A90, 0x163AE4], Level=[0x163A8B]} // Moltres in Mt.Ember
StaticPokemon{}={Species=[0x16244C, 0x162460, 0x1624A6], Level=[0x162462]} // Mewtwo in Unk. Dungeon
StaticPokemon{}={Species=[0x167F8D, 0x167F93, 0x160BDE, 0x160BE4], Level=[0x167F8F]} // Snorlax (Route 12 and S.S. Anne)
StaticPokemon{}={Species=[0x168099, 0x16809F], Level=[0x16809B]} // Snorlax (Route 16)
StaticPokemon{}={Species=[0x163BFE, 0x163C04], Level=[0x163C06]} // Hypno in Berry Forest
StaticPokemon{}={Species=[0x16522A, 0x16523A, 0x165284, 0x16528F], Level=[0x16523F]} // Deoxys on Birth Island
StaticPokemon{}={Species=[0x164F80, 0x164FAF, 0x164FFE, 0x165009], Level=[0x164FB4]} // Ho-Oh on Navel Rock
StaticPokemon{}={Species=[0x1650CE, 0x1650D9, 0x165123, 0x16512E], Level=[0x1650DE]} // Lugia on Navel Rock
StaticPokemon{}={Species=[0x16E72D, 0x16E731, 0x16E73C, 0x16E62E], Level=[0x16E73E]} // Old Amber
StaticPokemon{}={Species=[0x16E6A3, 0x16E6A7, 0x16E6B2, 0x16E5B2], Level=[0x16E6B4]} // Helix Fossil
StaticPokemon{}={Species=[0x16E6E8, 0x16E6EC, 0x16E6F7, 0x16E5F0], Level=[0x16E6F9]} // Dome Fossil
StaticPokemon{}={Species=[0x161A1A, 0x161A1D, 0x161A5F, 0x161A95], Level=[0x161A1F]} // Lapras in Silph. Co
StaticPokemon{}={Species=[0x16F70B, 0x16F70E, 0x16F7D0], Level=[0x16F710]} // Magikarp in Mt.Moon Center
StaticPokemon{}={Species=[0x16CB60, 0x16CBDC], Level=[0x16CC1F]} // Abra
StaticPokemon{}={Species=[0x16CB70, 0x16CBE7], Level=[0x16CC34]} // Clefairy
StaticPokemon{}={Species=[0x16CB80, 0x16CC13], Level=[0x16CC88]} // Pinsir
StaticPokemon{}={Species=[0x16CB90, 0x16CBF2], Level=[0x16CC49]} // Dratini
StaticPokemon{}={Species=[0x16CBA0, 0x16CC08], Level=[0x16CC73]} // Porygon
FossilLevelOffsets=[0x16E73E, 0x16E6B4, 0x16E6B4]
ShopItemOffsets=[0x1648FC, 0x167628, 0x167640, 0x16765C, 0x16767C, 0x16A1E0, 0x16A650, 0x16AC20, 0x16B2D8, 0x16B5D4, 0x16BA80, 0x16BABC, 0x16BB78, 0x16BBCC, 0x16BC04, 0x16D460, 0x16E990, 0x16EA3C, 0x16EF24, 0x170AA4, 0x171800, 0x171C20, 0x171DD8]
CRC32=D12F1FDD

[Fire Red (S)]
Game=BPRS
Version=0
Type=FRLG
CopyFrom=Fire Red (U) 1.0
PokemonMovesets=0x258F7C
EggMoves=0x25A6D4
PokemonTMHMCompat=0x24E390
PokemonEvolutions=0x254F1C
StarterPokemon=0x169C4C
TrainerData=0x23A234
TrainerClassNames=0x239CC4
MoveDescriptions=0x47EF78
TmMoves=0x454C1C
TmMovesDuplicate=0x454E84
MoveTutorData=0x4541D8
ItemImages=0x3CF48C
TmPals=[0xE91D98, 0xE91CF8, 0xE91F50, 0xE91DC0, 0xE91ED8, 0xE91F28, 0xE91E10, 0xE91F00, 0xE91E88, 0xE91F00, 0xE91E38, 0xE91D48, 0xE91E10, 0xE91EB0, 0xE91D70, 0xE91DE8, 0xE91D20, 0xE91E60]
IntroCryOffset=0x12FCB4
IntroSpriteOffset=0x13111C
IntroOtherOffset=0x1310C8
TradeTableOffset=0x268754
RunIndoorsTweakOffset=0xBD648
TextSpeedValuesOffset=0x41A090
CatchingTutorialOpponentMonOffset=0x7F8C4
PCPotionOffset=0x3FCB9C
TypeEffectivenessOffset=0x24A7BC
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x16C50A, 0x16C50D, 0x16C54D, 0x16C581], Level=[0x16C50F]} // Eevee in Celadon Condominiums
StaticPokemon{}={Species=[0x16ECA4, 0x16ECAB], Level=[0x16ED1E]} // Hitmonlee in Fighting Dojo
StaticPokemon{}={Species=[0x16ECEA, 0x16ECF1], Level=[0x16ED1E]} // Hitmonchan in Fighting Dojo
StaticPokemon{}={Species=[0x1638D2, 0x1638D9], Level=[0x1638D4]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x163930, 0x163937], Level=[0x163932]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x16385E, 0x163865, 0x1638B9], Level=[0x163860]} // Zapdos in The Power Plant
StaticPokemon{}={Species=[0x163252, 0x163259, 0x1632AD, 0x170138, 0x170144], Level=[0x163254]} // Articuno (Seafoam and Route 15 Gatehouse)
StaticPokemon{}={Species=[0x163BD9, 0x163BE0, 0x163C34], Level=[0x163BDB]} // Moltres in Mt.Ember
StaticPokemon{}={Species=[0x16259C, 0x1625B0, 0x1625F6], Level=[0x1625B2]} // Mewtwo in Unk. Dungeon
StaticPokemon{}={Species=[0x1680DD, 0x1680E3, 0x160D2E, 0x160D34], Level=[0x1680DF]} // Snorlax (Route 12 and S.S. Anne)
StaticPokemon{}={Species=[0x1681E9, 0x1681EF], Level=[0x1681EB]} // Snorlax (Route 16)
StaticPokemon{}={Species=[0x163D4E, 0x163D54], Level=[0x163D56]} // Hypno in Berry Forest
StaticPokemon{}={Species=[0x16537A, 0x16538A, 0x1653D4, 0x1653DF], Level=[0x16538F]} // Deoxys on Birth Island
StaticPokemon{}={Species=[0x1650D0, 0x1650FF, 0x16514E, 0x165159], Level=[0x165104]} // Ho-Oh on Navel Rock
StaticPokemon{}={Species=[0x16521E, 0x165229, 0x165273, 0x16527E], Level=[0x16522E]} // Lugia on Navel Rock
StaticPokemon{}={Species=[0x16E87D, 0x16E881, 0x16E88C, 0x16E77E], Level=[0x16E88E]} // Old Amber
StaticPokemon{}={Species=[0x16E7F3, 0x16E7F7, 0x16E802, 0x16E702], Level=[0x16E804]} // Helix Fossil
StaticPokemon{}={Species=[0x16E838, 0x16E83C, 0x16E847, 0x16E740], Level=[0x16E849]} // Dome Fossil
StaticPokemon{}={Species=[0x161B6A, 0x161B6D, 0x161BAF, 0x161BE5], Level=[0x161B6F]} // Lapras in Silph. Co
StaticPokemon{}={Species=[0x16F85B, 0x16F85E, 0x16F920], Level=[0x16F860]} // Magikarp in Mt.Moon Center
StaticPokemon{}={Species=[0x16CCB0, 0x16CD2C], Level=[0x16CD6F]} // Abra
StaticPokemon{}={Species=[0x16CCC0, 0x16CD37], Level=[0x16CD84]} // Clefairy
StaticPokemon{}={Species=[0x16CCE0, 0x16CD4D], Level=[0x16CDAE]} // Scyther
StaticPokemon{}={Species=[0x16CCD0, 0x16CD42], Level=[0x16CD99]} // Dratini
StaticPokemon{}={Species=[0x16CCF0, 0x16CD58], Level=[0x16CDC3]} // Porygon
FossilLevelOffsets=[0x16E88E, 0x16E804, 0x16E849]
ShopItemOffsets=[0x164A4C, 0x167778, 0x167790, 0x1677AC, 0x1677CC, 0x16A330, 0x16A7A0, 0x16AD70, 0x16B428, 0x16B724, 0x16BBD0, 0x16BC0C, 0x16BCC8, 0x16BD1C, 0x16BD54, 0x16D5B0, 0x16EAE0, 0x16EB8C, 0x16F074, 0x170BF4, 0x171950, 0x171D70, 0x171F28]
CRC32=9F08064E

[Leaf Green (S)]
Game=BPGS
Version=0
Type=FRLG
CopyFrom=Fire Red (S)
PokemonMovesets=0x258F5C
EggMoves=0x25A6B4
PokemonTMHMCompat=0x24E36C
PokemonEvolutions=0x254EFC
StarterPokemon=0x169C28
TrainerData=0x23A210
TrainerClassNames=0x239CA0
MoveDescriptions=0x47E670
TmMoves=0x4543AC
TmMovesDuplicate=0x454614
MoveTutorData=0x453968
ItemImages=0x3CF2C8
TmPals=[0xE91E18, 0xE91D78, 0xE91FD0, 0xE91E40, 0xE91F58, 0xE91FA8, 0xE91E90, 0xE91F80, 0xE91F08, 0xE91F80, 0xE91EB8, 0xE91DC8, 0xE91E90, 0xE91F30, 0xE91DF0, 0xE91E68, 0xE91DA0, 0xE91EE0]
IntroCryOffset=0x12FC8C
IntroSpriteOffset=0x1310F4
IntroOtherOffset=0x1310A0
TradeTableOffset=0x268734
RunIndoorsTweakOffset=0xBD61C
TextSpeedValuesOffset=0x419ECC
CatchingTutorialOpponentMonOffset=0x7F898
PCPotionOffset=0x3FC9D8
TypeEffectivenessOffset=0x24A798
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x16C4E6, 0x16C4E9, 0x16C529, 0x16C55D], Level=[0x16C4EB]} // Eevee in Celadon Condominiums
StaticPokemon{}={Species=[0x16EC80, 0x16EC87], Level=[0x16ECFA]} // Hitmonlee in Fighting Dojo
StaticPokemon{}={Species=[0x16ECC6, 0x16ECCD], Level=[0x16ECFA]} // Hitmonchan in Fighting Dojo
StaticPokemon{}={Species=[0x1638AE, 0x1638B5], Level=[0x1638B0]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x16390C, 0x163913], Level=[0x16390E]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x16383A, 0x163841, 0x163895], Level=[0x16383C]} // Zapdos in The Power Plant
StaticPokemon{}={Species=[0x16322E, 0x163235, 0x163289, 0x170114, 0x170120], Level=[0x163230]} // Articuno (Seafoam and Route 15 Gatehouse)
StaticPokemon{}={Species=[0x163BB5, 0x163BBC, 0x163C10], Level=[0x163BB7]} // Moltres in Mt.Ember
StaticPokemon{}={Species=[0x162578, 0x16258C, 0x1625D2], Level=[0x16258E]} // Mewtwo in Unk. Dungeon
StaticPokemon{}={Species=[0x1680B9, 0x1680BF, 0x160D0A, 0x160D10], Level=[0x1680BB]} // Snorlax (Route 12 and S.S. Anne)
StaticPokemon{}={Species=[0x1681C5, 0x1681CB], Level=[0x1681C7]} // Snorlax (Route 16)
StaticPokemon{}={Species=[0x163D2A, 0x163D30], Level=[0x163D32]} // Hypno in Berry Forest
StaticPokemon{}={Species=[0x165356, 0x165366, 0x1653B0, 0x1653BB], Level=[0x16536B]} // Deoxys on Birth Island
StaticPokemon{}={Species=[0x1650AC, 0x1650DB, 0x16512A, 0x165135], Level=[0x1650E0]} // Ho-Oh on Navel Rock
StaticPokemon{}={Species=[0x1651FA, 0x165205, 0x16524F, 0x16525A], Level=[0x16520A]} // Lugia on Navel Rock
StaticPokemon{}={Species=[0x16E859, 0x16E85D, 0x16E868, 0x16E75A], Level=[0x16E86A]} // Old Amber
StaticPokemon{}={Species=[0x16E7CF, 0x16E7D3, 0x16E7DE, 0x16E6DE], Level=[0x16E7E0]} // Helix Fossil
StaticPokemon{}={Species=[0x16E814, 0x16E818, 0x16E823, 0x16E71C], Level=[0x16E825]} // Dome Fossil
StaticPokemon{}={Species=[0x161B46, 0x161B49, 0x161B8B, 0x161BC1], Level=[0x161B4B]} // Lapras in Silph. Co
StaticPokemon{}={Species=[0x16F837, 0x16F83A, 0x16F8FC], Level=[0x16F83C]} // Magikarp in Mt.Moon Center
StaticPokemon{}={Species=[0x16CC8C, 0x16CD08], Level=[0x16CD4B]} // Abra
StaticPokemon{}={Species=[0x16CC9C, 0x16CD13], Level=[0x16CD60]} // Clefairy
StaticPokemon{}={Species=[0x16CCAC, 0x16CD3F], Level=[0x16CDB4]} // Pinsir
StaticPokemon{}={Species=[0x16CCBC, 0x16CD1E], Level=[0x16CD75]} // Dratini
StaticPokemon{}={Species=[0x16CCCC, 0x16CD34], Level=[0x16CD9F]} // Porygon
FossilLevelOffsets=[0x16E86A, 0x16E7E0, 0x16E825]
ShopItemOffsets=[0x164A28, 0x167754, 0x16776C, 0x167788, 0x1677A8, 0x16A30C, 0x16A77C, 0x16AD4C, 0x16B404, 0x16B700, 0x16BBAC, 0x16BBE8, 0x16BCA4, 0x16BCF8, 0x16BD30, 0x16D58C, 0x16EABC, 0x16EB68, 0x16F050, 0x170BD0, 0x17192C, 0x171D4C, 0x171F04]
CRC32=2CA11D59

[Fire Red (I)]
Game=BPRI
Version=0
Type=FRLG
CopyFrom=Fire Red (U) 1.0
PokemonMovesets=0x256894
EggMoves=0x257FEC
PokemonTMHMCompat=0x24BCA8
PokemonEvolutions=0x252834
StarterPokemon=0x169B60
TrainerData=0x237B6C
TrainerClassNames=0x2375FC
MoveDescriptions=0x47C174
TmMoves=0x451580
TmMovesDuplicate=0x4517E8
MoveTutorData=0x450B3C
ItemImages=0x3CCDA4
TmPals=[0xE91DD4, 0xE91D34, 0xE91F8C, 0xE91DFC, 0xE91F14, 0xE91F64, 0xE91E4C, 0xE91F3C, 0xE91EC4, 0xE91F3C, 0xE91E74, 0xE91D84, 0xE91E4C, 0xE91EEC, 0xE91DAC, 0xE91E24, 0xE91D5C, 0xE91E9C]
IntroCryOffset=0x12FBC8
IntroSpriteOffset=0x131030
IntroOtherOffset=0x130FDC
TradeTableOffset=0x26606C
RunIndoorsTweakOffset=0xBD560
TextSpeedValuesOffset=0x4169E8
CatchingTutorialOpponentMonOffset=0x7F7DC
PCPotionOffset=0x3F99B0
TypeEffectivenessOffset=0x2480F4
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x16C41E, 0x16C421, 0x16C461, 0x16C495], Level=[0x16C423]} // Eevee in Celadon Condominiums
StaticPokemon{}={Species=[0x16EBB8, 0x16EBBF], Level=[0x16EC32]} // Hitmonlee in Fighting Dojo
StaticPokemon{}={Species=[0x16EBFE, 0x16EC05], Level=[0x16EC32]} // Hitmonchan in Fighting Dojo
StaticPokemon{}={Species=[0x1637E6, 0x1637ED], Level=[0x1637E8]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x163844, 0x16384B], Level=[0x163846]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x163772, 0x163779, 0x1637CD], Level=[0x163774]} // Zapdos in The Power Plant
StaticPokemon{}={Species=[0x163166, 0x16316D, 0x1631C1, 0x17004C, 0x170058], Level=[0x163168]} // Articuno (Seafoam and Route 15 Gatehouse)
StaticPokemon{}={Species=[0x163AED, 0x163AF4, 0x163B48], Level=[0x163AEF]} // Moltres in Mt.Ember
StaticPokemon{}={Species=[0x1624B0, 0x1624C4, 0x16250A], Level=[0x1624C6]} // Mewtwo in Unk. Dungeon
StaticPokemon{}={Species=[0x167FF1, 0x167FF7, 0x160C42, 0x160C48], Level=[0x167FF3]} // Snorlax (Route 12 and S.S. Anne)
StaticPokemon{}={Species=[0x1680FD, 0x168103], Level=[0x1680FF]} // Snorlax (Route 16)
StaticPokemon{}={Species=[0x163C62, 0x163C68], Level=[0x163C6A]} // Hypno in Berry Forest
StaticPokemon{}={Species=[0x16528E, 0x16529E, 0x1652E8, 0x1652F3], Level=[0x1652A3]} // Deoxys on Birth Island
StaticPokemon{}={Species=[0x164FE4, 0x165013, 0x165062, 0x16506D], Level=[0x165018]} // Ho-Oh on Navel Rock
StaticPokemon{}={Species=[0x165132, 0x16513D, 0x165187, 0x165192], Level=[0x165142]} // Lugia on Navel Rock
StaticPokemon{}={Species=[0x16E791, 0x16E795, 0x16E7A0, 0x16E692], Level=[0x16E7A2]} // Old Amber
StaticPokemon{}={Species=[0x16E707, 0x16E70B, 0x16E716, 0x16E616], Level=[0x16E718]} // Helix Fossil
StaticPokemon{}={Species=[0x16E74C, 0x16E750, 0x16E75B, 0x16E654], Level=[0x16E75D]} // Dome Fossil
StaticPokemon{}={Species=[0x161A7E, 0x161A81, 0x161AC3, 0x161AF9], Level=[0x161A83]} // Lapras in Silph. Co
StaticPokemon{}={Species=[0x16F76F, 0x16F772, 0x16F834], Level=[0x16F774]} // Magikarp in Mt.Moon Center
StaticPokemon{}={Species=[0x16CBC4, 0x16CC40], Level=[0x16CC83]} // Abra
StaticPokemon{}={Species=[0x16CBD4, 0x16CC4B], Level=[0x16CC98]} // Clefairy
StaticPokemon{}={Species=[0x16CBF4, 0x16CC61], Level=[0x16CCC2]} // Scyther
StaticPokemon{}={Species=[0x16CBE4, 0x16CC56], Level=[0x16CCAD]} // Dratini
StaticPokemon{}={Species=[0x16CC04, 0x16CC6C], Level=[0x16CCD7]} // Porygon
FossilLevelOffsets=[0x16E7A2, 0x16E718, 0x16E75D]
ShopItemOffsets=[0x164960, 0x16768C, 0x1676A4, 0x1676C0, 0x1676E0, 0x16A244, 0x16A6B4, 0x16AC84, 0x16B33C, 0x16B638, 0x16BAE4, 0x16BB20, 0x16BBDC, 0x16BC30, 0x16BC68, 0x16D4C4, 0x16E9F4, 0x16EAA0, 0x16EF88, 0x170B08, 0x171864, 0x171C84, 0x171E3C]
CRC32=73A72167

[Leaf Green (I)]
Game=BPGI
Version=0
Type=FRLG
CopyFrom=Fire Red (I)
PokemonMovesets=0x256874
EggMoves=0x257FCC
PokemonTMHMCompat=0x24BC84
PokemonEvolutions=0x252814
StarterPokemon=0x169B3C
TrainerData=0x237B48
TrainerClassNames=0x2375D8
MoveDescriptions=0x47B90C
TmMoves=0x450DB0
TmMovesDuplicate=0x451018
MoveTutorData=0x45036C
ItemImages=0x3CCBE0
TmPals=[0xE91E54, 0xE91DB4, 0xE9200C, 0xE91E7C, 0xE91F94, 0xE91FE4, 0xE91ECC, 0xE91FBC, 0xE91F44, 0xE91FBC, 0xE91EF4, 0xE91E04, 0xE91ECC, 0xE91F6C, 0xE91E2C, 0xE91EA4, 0xE91DDC, 0xE91F1C]
IntroCryOffset=0x12FBA0
IntroSpriteOffset=0x131008
IntroOtherOffset=0x130FB4
TradeTableOffset=0x26604C
RunIndoorsTweakOffset=0xBD534
TextSpeedValuesOffset=0x416824
CatchingTutorialOpponentMonOffset=0x7F7B0
PCPotionOffset=0x3F97EC
TypeEffectivenessOffset=0x2480D0
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x16C3FA, 0x16C3FD, 0x16C43D, 0x16C471], Level=[0x16C3FF]} // Eevee in Celadon Condominiums
StaticPokemon{}={Species=[0x16EB94, 0x16EB9B], Level=[0x16EC0E]} // Hitmonlee in Fighting Dojo
StaticPokemon{}={Species=[0x16EBDA, 0x16EBE1], Level=[0x16EC0E]} // Hitmonchan in Fighting Dojo
StaticPokemon{}={Species=[0x1637C2, 0x1637C9], Level=[0x1637C4]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x163820, 0x163827], Level=[0x163822]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x16374E, 0x163755, 0x1637A9], Level=[0x163750]} // Zapdos in The Power Plant
StaticPokemon{}={Species=[0x163142, 0x163149, 0x16319D, 0x170028, 0x170034], Level=[0x163144]} // Articuno (Seafoam and Route 15 Gatehouse)
StaticPokemon{}={Species=[0x163AC9, 0x163AD0, 0x163B24], Level=[0x163ACB]} // Moltres in Mt.Ember
StaticPokemon{}={Species=[0x16248C, 0x1624A0, 0x1624E6], Level=[0x1624A2]} // Mewtwo in Unk. Dungeon
StaticPokemon{}={Species=[0x167FCD, 0x167FD3, 0x160C1E, 0x160C24], Level=[0x167FCF]} // Snorlax (Route 12 and S.S. Anne)
StaticPokemon{}={Species=[0x1680D9, 0x1680DF], Level=[0x1680DB]} // Snorlax (Route 16)
StaticPokemon{}={Species=[0x163C3E, 0x163C44], Level=[0x163C46]} // Hypno in Berry Forest
StaticPokemon{}={Species=[0x16526A, 0x16527A, 0x1652C4, 0x1652CF], Level=[0x16527F]} // Deoxys on Birth Island
StaticPokemon{}={Species=[0x164FC0, 0x164FEF, 0x16503E, 0x165049], Level=[0x164FF4]} // Ho-Oh on Navel Rock
StaticPokemon{}={Species=[0x16510E, 0x165119, 0x165163, 0x16516E], Level=[0x16511E]} // Lugia on Navel Rock
StaticPokemon{}={Species=[0x16E76D, 0x16E771, 0x16E77C, 0x16E66E], Level=[0x16E77E]} // Old Amber
StaticPokemon{}={Species=[0x16E6E3, 0x16E6E7, 0x16E6F2, 0x16E5F2], Level=[0x16E6F4]} // Helix Fossil
StaticPokemon{}={Species=[0x16E728, 0x16E72C, 0x16E737, 0x16E630], Level=[0x16E739]} // Dome Fossil
StaticPokemon{}={Species=[0x161A5A, 0x161A5D, 0x161A9F, 0x161AD5], Level=[0x161A5F]} // Lapras in Silph. Co
StaticPokemon{}={Species=[0x16F74B, 0x16F74E, 0x16F810], Level=[0x16F750]} // Magikarp in Mt.Moon Center
StaticPokemon{}={Species=[0x16CBA0, 0x16CC1C], Level=[0x16CC5F]} // Abra
StaticPokemon{}={Species=[0x16CBB0, 0x16CC27], Level=[0x16CC74]} // Clefairy
StaticPokemon{}={Species=[0x16CBC0, 0x16CC53], Level=[0x16CCC8]} // Pinsir
StaticPokemon{}={Species=[0x16CBD0, 0x16CC32], Level=[0x16CC89]} // Dratini
StaticPokemon{}={Species=[0x16CBE0, 0x16CC48], Level=[0x16CCB3]} // Porygon
FossilLevelOffsets=[0x16E77E, 0x16E6F4, 0x16E739]
ShopItemOffsets=[0x16493C, 0x167668, 0x167680, 0x16769C, 0x1676BC, 0x16A220, 0x16A690, 0x16AC60, 0x16B318, 0x16B614, 0x16BAC0, 0x16BAFC, 0x16BBB8, 0x16BC0C, 0x16BC44, 0x16D4A0, 0x16E9D0, 0x16EA7C, 0x16EF64, 0x170AE4, 0x171840, 0x171C60, 0x171E18]
CRC32=16974506

[Ruby (J)]
Game=AXVJ
Version=0
Type=Ruby
TableFile=gba_jpn
FreeSpace=0x661000
PokemonCount=411
PokemonNameLength=6
PokemonStats=0x1D09CC
PokemonMovesets=0x1D997C
EggMoves=0x1DAF78
PokemonTMHMCompat=0x1CEEA4
PokemonEvolutions=0x1D591C
StarterPokemon=0x3D25C8
StarterItems=0x7F072
TrainerData=0x1C4C94
TrainerEntrySize=32
TrainerCount=0x2B6
TrainerClassNames=0x1C4A14
TrainerClassCount=58
TrainerClassNameLength=11
TrainerNameLength=6
DoublesTrainerClasses=[27, 42, 55, 56, 57]
EliteFourIndices=[261, 262, 263, 264, 335]
ItemData=0x39A648
ItemEntrySize=40
ItemCount=348
MoveCount=354
MoveData=0x1CCEE0
MoveNameLength=8
MoveNames=0x1CACFC
AbilityNameLength=8
AbilityNames=0x1CBC44
TmMoves=0x35017C
IntroCryOffset=0x7AAA
IntroSpriteOffset=0x8838
IntroPaletteOffset=0x8844
IntroOtherOffset=0x8806
PokemonFrontSprites=0x1BCB60
PokemonNormalPalettes=0x1BEDC0
ItemBallPic=59
TradeTableOffset=0x1E9C48
TradeTableSize=3
TradesUnused=[]
RunIndoorsTweakOffset=0xE0D88
CatchingTutorialOpponentMonOffset=0x7E9D8
CatchingTutorialPlayerMonOffset=0x10A606
PCPotionOffset=0x3E177C
PickupTableStartLocator=16001E00170028000200320044003C
PickupItemCount=10
TypeEffectivenessOffset=0x1CB9BC
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x162B23, 0x162B50], Level=[0x162B52]} // Lileep
StaticPokemon{}={Species=[0x162B75, 0x162BA2], Level=[0x162BA4]} // Anorith
StaticPokemon{}={Species=[0x174A01, 0x1800AD, 0x1749FA], Level=[0x1749FC]} // Groudon
StaticPokemon{}={Species=[0x171D3A, 0x171D43], Level=[0x171D45]} // Regirock
StaticPokemon{}={Species=[0x1767CC, 0x1767D5], Level=[0x1767D7]} // Regice
StaticPokemon{}={Species=[0x17687F, 0x176888], Level=[0x17688A]} // Registeel
StaticPokemon{}={Species=[0x179434, 0x17945A], Level=[0x17945C]} // Latias (Southern Island)
StaticPokemon{}={Species=[0x176B44, 0x176B4B], Level=[0x176B46]} // Rayquaza
StaticPokemon{}={Species=[0x1801EC, 0x1801F5], Level=[0x1801F7]} // Kecleons on OW (7)
StaticPokemon{}={Species=[0x153337, 0x153340], Level=[0x153342]} // Kecleon w/ Steven
StaticPokemon{}={Species=[0x175ADB, 0x175AE2], Level=[0x175ADD]} // Voltorb 1
StaticPokemon{}={Species=[0x175AF9, 0x175B00], Level=[0x175AFB]} // Voltorb 2
StaticPokemon{}={Species=[0x175B17, 0x175B1E], Level=[0x175B19]} // Voltorb 3
StaticPokemon{}={Species=[0x18010A, 0x180111], Level=[0x18010C]} // Electrode 1
StaticPokemon{}={Species=[0x180128, 0x18012F], Level=[0x18012A]} // Electrode 2
StaticPokemon{}={Species=[0x14CF1E]} // Wynaut Egg
StaticPokemon{}={Species=[0x16C023, 0x16C033], Level=[0x16C025]} // Beldum
StaticPokemon{}={Species=[0x17E914], Level=[0x17E916]} // Castform
RoamingPokemon{}={Species=[0x10B89C, 0x12F07C], Level=[0x12EFE0, 0x12EFF0]} // Latios
StaticEggPokemonOffsets=[15]
FindMapsWithMonFunctionStartOffset=0x10B81C
CreateInitialRoamerMonFunctionStartOffset=0x12EFC4
ShopItemOffsets=[0x146004, 0x1463E4, 0x1566AC, 0x1566C8, 0x157954, 0x158280, 0x158448, 0x15932C, 0x15CA14, 0x15CA40, 0x160330, 0x162680, 0x1642EC, 0x164314, 0x1653B4, 0x16A694, 0x16A6C8, 0x16A78C, 0x16A7B4, 0x16A900, 0x16A924, 0x16BCF0, 0x16D828, 0x16F184]
SkipShops=[1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23]
MainGameShops=[0, 4, 17, 18]
CRC32=CEE9471A

[Sapphire (J)]
Game=AXPJ
Version=0
Type=Sapp
CopyFrom=Ruby (J)
PokemonStats=0x1D095C
PokemonMovesets=0x1D990C
EggMoves=0x1DAF08
PokemonTMHMCompat=0x1CEE34
PokemonEvolutions=0x1D58AC
StarterPokemon=0x3D25AC
TrainerData=0x1C4C24
TrainerClassNames=0x1C49A4
ItemData=0x39A62C
MoveData=0x1CCE70
MoveNames=0x1CAC8C
AbilityNames=0x1CBBD4
TmMoves=0x35010C
PokemonFrontSprites=0x1BCAF0
PokemonNormalPalettes=0x1BED50
TradeTableOffset=0x1E9BD8
RunIndoorsTweakOffset=0xE0D88
PCPotionOffset=0x3E1760
TypeEffectivenessOffset=0x1CB94C
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x162AB3, 0x162AE0], Level=[0x162AE2]} // Lileep
StaticPokemon{}={Species=[0x162B05, 0x162B32], Level=[0x162B34]} // Anorith
StaticPokemon{}={Species=[0x174991, 0x18003D, 0x17498A], Level=[0x17498C]} // Kyogre
StaticPokemon{}={Species=[0x171CCA, 0x171CD3], Level=[0x171CD5]} // Regirock
StaticPokemon{}={Species=[0x17675C, 0x176765], Level=[0x176767]} // Regice
StaticPokemon{}={Species=[0x17680F, 0x176818], Level=[0x17681A]} // Registeel
StaticPokemon{}={Species=[0x1793C4, 0x1793EA], Level=[0x1793EC]} // Latios (Southern Island)
StaticPokemon{}={Species=[0x176AD4, 0x176ADB], Level=[0x176AD6]} // Rayquaza
StaticPokemon{}={Species=[0x18017C, 0x180185], Level=[0x180187]} // Kecleons on OW (7)
StaticPokemon{}={Species=[0x1532CB, 0x1532D4], Level=[0x1532D6]} // Kecleon w/ Steven
StaticPokemon{}={Species=[0x175A6B, 0x175A72], Level=[0x175A6D]} // Voltorb 1
StaticPokemon{}={Species=[0x175A89, 0x175A90], Level=[0x175A8B]} // Voltorb 2
StaticPokemon{}={Species=[0x175AA7, 0x175AAE], Level=[0x175AA9]} // Voltorb 3
StaticPokemon{}={Species=[0x18009A, 0x1800A1], Level=[0x18009C]} // Electrode 1
StaticPokemon{}={Species=[0x1800B8, 0x1800BF], Level=[0x1800BA]} // Electrode 2
StaticPokemon{}={Species=[0x14CEB2]} // Wynaut Egg
StaticPokemon{}={Species=[0x16BFB3, 0x16BFC3], Level=[0x16BFB5]} // Beldum
StaticPokemon{}={Species=[0x17E8A4], Level=[0x17E8A6]} // Castform
RoamingPokemon{}={Species=[0x10B898, 0x12F080], Level=[0x12EFDE, 0x12EFEE]} // Latias
ShopItemOffsets=[0x146004, 0x1463E4, 0x15663C, 0x156658, 0x1578E4, 0x158210, 0x1583D8, 0x1592BC, 0x15C9A4, 0x15C9D0, 0x1602C0, 0x162610, 0x16427C, 0x1642A4, 0x165344, 0x16A624, 0x16A658, 0x16A71C, 0x16A744, 0x16A890, 0x16A8B4, 0x16BC80, 0x16D7B8, 0x16F114]
CRC32=FD1EEB78

[Emerald (J)]
Game=BPEJ
Version=0
Type=Em
TableFile=gba_jpn
FreeSpace=0xE40000
PokemonCount=411
PokemonNameLength=6
PokemonMovesets=0x2F9D04
EggMoves=0x2FB764
PokemonTMHMCompat=0x2EF220
PokemonEvolutions=0x2F5CA4
StarterPokemon=0x590C08
StarterItems=0xB0A66
TrainerData=0x2E383C
TrainerEntrySize=32
TrainerCount=0x357
TrainerClassNames=0x2E3564
TrainerClassCount=66
TrainerClassNameLength=11
TrainerNameLength=6
DoublesTrainerClasses=[34, 46, 55, 56, 57]
EliteFourIndices=[261, 262, 263, 264, 335]
MossdeepStevenTeamOffset=0x5BC614
ItemEntrySize=40
ItemCount=376
MoveCount=354
MoveNameLength=8
AbilityNameLength=8
TmMoves=0x5E144C
TmMovesDuplicate=0x5E18F8
MoveTutorData=0x5E08C4
MoveTutorMoves=30
ItemImages=0x5DFCC8
TmPals=[0xDB613C, 0xDB609C, 0xDB62F4, 0xDB6164, 0xDB627C, 0xDB62CC, 0xDB61B4, 0xDB62A4, 0xDB622C, 0xDB62A4, 0xDB61DC, 0xDB60EC, 0xDB61B4, 0xDB6254, 0xDB6114, 0xDB618C, 0xDB60C4, 0xDB6204]
IntroCryOffset=0x3084C
IntroSpriteOffset=0x31664
ItemBallPic=59
TradeTableOffset=0x30D114
TradeTableSize=4
TradesUnused=[]
RunIndoorsTweakOffset=0x11AA38
TextSpeedValuesOffset=0x5D7B24
CatchingTutorialOpponentMonOffset=0xB016C
CatchingTutorialPlayerMonOffset=0x1394E2
PCPotionOffset=0x5C0BE0
PickupTableStartLocator=0D000E0016000300560055
PickupItemCount=29
TypeEffectivenessOffset=0x2EBB38
DeoxysStatPrefix=8AAE6C78FF00
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x2015EE, 0x201613, 0x201616, 0x201698, 0x2016A6], Level=[0x201618]} // Lileep
StaticPokemon{}={Species=[0x201600, 0x2016B6, 0x2016B9, 0x20173B, 0x201749], Level=[0x2016BB]} // Anorith
StaticPokemon{}={Species=[0x21D057, 0x21D065, 0x21D0BA, 0x1E201C, 0x1E209C, 0x1E2158, 0x1E21D8], Level=[0x21D067]} // Kyogre
StaticPokemon{}={Species=[0x21D128, 0x21D136, 0x21D18B, 0x1E205C, 0x1E2198], Level=[0x21D138]} // Groudon
StaticPokemon{}={Species=[0x213E8A, 0x213E93, 0x213ED9], Level=[0x213E95]} // Regirock
StaticPokemon{}={Species=[0x21B8A1, 0x21B8AA, 0x21B8F0], Level=[0x21B8AC]} // Regice
StaticPokemon{}={Species=[0x21B9A3, 0x21B9AC, 0x21B9F2], Level=[0x21B9AE]} // Registeel
StaticPokemon{}={Species=[0x21BF95, 0x21BF9E, 0x21BFE4, 0x21C033, 0x21C051, 0x1E22FA, 0x1E2318, 0x1E23AE, 0x1E23CC], Level=[0x21BFA0]} // Rayquaza
StaticPokemon{}={Species=[0x243407, 0x243410], Level=[0x243412]} // Kecleons on OW (7)
StaticPokemon{}={Species=[0x1EDB4E, 0x1EDB57], Level=[0x1EDB59]} // Kecleon w/ Steven
StaticPokemon{}={Species=[0x21A80D, 0x21A814], Level=[0x21A80F]} // Voltorb 1
StaticPokemon{}={Species=[0x21A85A, 0x21A861], Level=[0x21A85C]} // Voltorb 2
StaticPokemon{}={Species=[0x21A8A7, 0x21A8AE], Level=[0x21A8A9]} // Voltorb 3
StaticPokemon{}={Species=[0x217CF7, 0x217CFE], Level=[0x217CF9]} // Electrode 1
StaticPokemon{}={Species=[0x217D44, 0x217D4B], Level=[0x217D46]} // Electrode 2
StaticPokemon{}={Species=[0x222AB7, 0x222AC5], Level=[0x222AC7]} // Sudowoodo in Battle Frontier
StaticPokemon{}={Species=[0x22282F, 0x222944], Level=[0x222949]} // Latios on Southern Island
StaticPokemon{}={Species=[0x22283A, 0x222957], Level=[0x22295C]} // Latias on Southern Island
StaticPokemon{}={Species=[0x23B6A0, 0x23B6B0, 0x23B6FA, 0x23B705], Level=[0x23B6B5]} // Deoxys on Birth Island
StaticPokemon{}={Species=[0x23B4DB, 0x23B515, 0x23B56A, 0x23B575], Level=[0x23B51A]} // Mew on Faraway Island
StaticPokemon{}={Species=[0x23C1AE, 0x23C1DD, 0x23C22C, 0x23C237], Level=[0x23C1E2]} // Ho-Oh on Navel Rock
StaticPokemon{}={Species=[0x23C2F6, 0x23C301, 0x23C34B, 0x23C356], Level=[0x23C306]} // Lugia on Navel Rock
StaticPokemon{}={Species=[0x1E5912]} // Wynaut Egg
StaticPokemon{}={Species=[0x20C8FA, 0x20C8FD, 0x20C97F, 0x20C990], Level=[0x20C8FF]} // Beldum
StaticPokemon{}={Species=[0x24161F, 0x241622, 0x2416AE], Level=[0x241624]} // Castform
RoamingPokemon{}={Species=[0x161AC4], Level=[0x161AF6, 0x161B02]} // Latios
RoamingPokemon{}={Species=[0x161ACC], Level=[0x161AF6, 0x161B02]} // Latias
StaticEggPokemonOffsets=[22]
CreateInitialRoamerMonFunctionStartOffset=0x161AA8
StaticSouthernIslandOffsets=[16, 17]
ShopItemOffsets=[0x1DC148, 0x1DC564, 0x1F2B2C, 0x1F2B48, 0x1F41A0, 0x1F4EE4, 0x1F50B0, 0x1F6F40, 0x1FAA0C, 0x1FAA38, 0x1FE914, 0x200F68, 0x20360C, 0x203634, 0x204DD8, 0x20A954, 0x20A988, 0x20AA34, 0x20AA5C, 0x20AB84, 0x20ABA8, 0x20C5CC, 0x20F1D0, 0x211124, 0x23B264, 0x23BA34, 0x23BA60]
SkipShops=[1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24, 25, 26]
MainGameShops=[0, 4, 17, 18]
CRC32=4881F3F8

[Emerald (T-Eng)]
Game=BPET
Version=0
Type=Em
CopyStaticPokemon=1
CopyFrom=Emerald (J)

[Fire Red (J)]
Game=BPRJ
Version=0
Type=FRLG
TableFile=gba_jpn
FreeSpace=0x800000
PokemonCount=411
PokemonNameLength=6
PokemonMovesets=0x21A1BC
EggMoves=0x21B918
PokemonTMHMCompat=0x20F5D0
PokemonEvolutions=0x21615C
BattleTrappersBanned=[55,56,57,58,59]
StarterPokemon=0x17D1D0
TrainerData=0x1FDFD8
TrainerEntrySize=32
TrainerCount=0x2E7
TrainerClassNames=0x1FDB3C
TrainerClassCount=107
TrainerClassNameLength=11
TrainerNameLength=6
DoublesTrainerClasses=[26, 40, 52, 53, 54, 92, 93, 94, 95, 96]
EliteFourIndices=[410, 411, 412, 413, 438, 439, 440]
ItemEntrySize=40
ItemCount=374
MoveCount=354
MoveNameLength=8
AbilityNameLength=8
TmMoves=0x419D34
TmMovesDuplicate=0x419F9C
MoveTutorData=0x4192F0
MoveTutorMoves=15
ItemImages=0x39C79C
TmPals=[0xD91E8C, 0xD91DEC, 0xD92044, 0xD91EB4, 0xD91FCC, 0xD9201C, 0xD91F04, 0xD91FF4, 0xD91F7C, 0xD91FF4, 0xD91F2C, 0xD91E3C, 0xD91F04, 0xD91FA4, 0xD91E64, 0xD91EDC, 0xD91E14, 0xD91F54]
IntroCryOffset=0x13034C
IntroSpriteOffset=0x1317B4
IntroOtherOffset=0x131760
ItemBallPic=92
TradeTableOffset=0x22D2F8
TradeTableSize=9
TradesUnused=[]
RunIndoorsTweakOffset=0xBE754
TextSpeedValuesOffset=0x3E30CC
CatchingTutorialOpponentMonOffset=0x7EFB4
PCPotionOffset=0x3C83F8
PickupTableStartLocator=8B000F00850019008600230087002D
PickupItemCount=16
TypeEffectivenessOffset=0x20BF24
DeoxysStatPrefix=7F002301FFFF
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x184BB8, 0x184BBB, 0x184BFB, 0x184C2F], Level=[0x184BBD]} // Eevee in Celadon Condominiums
StaticPokemon{}={Species=[0x18A2E0, 0x18A2E7], Level=[0x18A35A]} // Hitmonlee in Fighting Dojo
StaticPokemon{}={Species=[0x18A326, 0x18A32D], Level=[0x18A35A]} // Hitmonchan in Fighting Dojo
StaticPokemon{}={Species=[0x16C18B, 0x16C192], Level=[0x16C18D]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x16C1E9, 0x16C1F0], Level=[0x16C1EB]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x16C117, 0x16C11E, 0x16C172], Level=[0x16C119]} // Zapdos in The Power Plant
StaticPokemon{}={Species=[0x16B2B8, 0x16B2BF, 0x16B313, 0x18DE82, 0x18DE8E], Level=[0x16B2BA]} // Articuno (Seafoam and Route 15 Gatehouse)
StaticPokemon{}={Species=[0x16C846, 0x16C84D, 0x16C8A1], Level=[0x16C848]} // Moltres in Mt.Ember
StaticPokemon{}={Species=[0x1693E1, 0x1693F5, 0x16943B], Level=[0x1693F7]} // Mewtwo in Unk. Dungeon
StaticPokemon{}={Species=[0x1769F0, 0x1769F7, 0x16505E, 0x165064], Level=[0x1769F2]} // Snorlax (Route 12 and S.S. Anne)
StaticPokemon{}={Species=[0x177A5E, 0x177A65], Level=[0x177A60]} // Snorlax (Route 16)
StaticPokemon{}={Species=[0x16C9CA, 0x16C9D0], Level=[0x16C9D2]} // Hypno in Berry Forest
StaticPokemon{}={Species=[0x16F243, 0x16F253, 0x16F29D, 0x16F2A8], Level=[0x16F258]} // Deoxys on Birth Island
StaticPokemon{}={Species=[0x16EF99, 0x16EFC8, 0x16F017, 0x16F022], Level=[0x16EFCD]} // Ho-Oh on Navel Rock
StaticPokemon{}={Species=[0x16F0E7, 0x16F0F2, 0x16F13C, 0x16F147], Level=[0x16F0F7]} // Lugia on Navel Rock
StaticPokemon{}={Species=[0x18959C, 0x1895A0, 0x1895AB, 0x18949D], Level=[0x1895AD]} // Old Amber
StaticPokemon{}={Species=[0x189512, 0x189516, 0x189521, 0x189421], Level=[0x189523]} // Helix Fossil
StaticPokemon{}={Species=[0x189557, 0x18955B, 0x189566, 0x18945F], Level=[0x189568]} // Dome Fossil
StaticPokemon{}={Species=[0x1675D5, 0x1675D8, 0x167617, 0x16764A], Level=[0x1675DA]} // Lapras in Silph. Co
StaticPokemon{}={Species=[0x18C665, 0x18C668, 0x18C727], Level=[0x18C66A]} // Magikarp in Mt.Moon Center
StaticPokemon{}={Species=[0x185A8A, 0x185B06], Level=[0x185B49]} // Abra
StaticPokemon{}={Species=[0x185A9A, 0x185B11], Level=[0x185B5E]} // Clefairy
StaticPokemon{}={Species=[0x185ABA, 0x185B27], Level=[0x185B88]} // Scyther
StaticPokemon{}={Species=[0x185AAA, 0x185B1C], Level=[0x185B73]} // Dratini
StaticPokemon{}={Species=[0x185ACA, 0x185B32], Level=[0x185B9D]} // Porygon
FossilLevelOffsets=[0x1895AD, 0x189523, 0x189568]
ShopItemOffsets=[0x16E72C, 0x17409C, 0x1740B4, 0x1740D0, 0x1740F0, 0x17EFAC, 0x17FE28, 0x1810D0, 0x182210, 0x182D44, 0x183948, 0x183984, 0x183CF0, 0x183E08, 0x183E40, 0x186E28, 0x189AC8, 0x189BBC, 0x18B014, 0x18F494, 0x191518, 0x191DFC, 0x192354]
SkipShops=[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 15, 16, 17, 18, 19, 20, 21, 22]
MainGameShops=[12, 13, 14]
CRC32=3B2056E9

[Fire Red (J) 1.1]
Game=BPRJ
Version=1
Type=FRLG
CopyFrom=Fire Red (J)
PokemonMovesets=0x2159D4
EggMoves=0x217130
PokemonTMHMCompat=0x20ADE8
PokemonEvolutions=0x211974
StarterPokemon=0x17CD8E
TrainerData=0x1F97F0
TrainerClassNames=0x1F9354
TmMoves=0x415634
TmMovesDuplicate=0x41589C
MoveTutorData=0x414BF0
ItemImages=0x397F5C
IntroCryOffset=0x13029C
IntroSpriteOffset=0x131704
IntroOtherOffset=0x1316B0
TradeTableOffset=0x228B10
RunIndoorsTweakOffset=0xBE6CC
TextSpeedValuesOffset=0x3DEA24
CatchingTutorialOpponentMonOffset=0x7EF44
PCPotionOffset=0x3C3D50
TypeEffectivenessOffset=0x20773C
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x184774, 0x184777, 0x1847B7, 0x1847EB], Level=[0x184779]} // Eevee in Celadon Condominiums
StaticPokemon{}={Species=[0x189E9C, 0x189EA3], Level=[0x189F16]} // Hitmonlee in Fighting Dojo
StaticPokemon{}={Species=[0x189EE2, 0x189EE9], Level=[0x189F16]} // Hitmonchan in Fighting Dojo
StaticPokemon{}={Species=[0x16BD20, 0x16BD27], Level=[0x16BD22]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x16BD7E, 0x16BD85], Level=[0x16BD80]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x16BCAC, 0x16BCB3, 0x16BD07], Level=[0x16BCAE]} // Zapdos in The Power Plant
StaticPokemon{}={Species=[0x16AE48, 0x16AE4F, 0x16AEA3, 0x18DA3E, 0x18DA4A], Level=[0x16AE4A]} // Articuno (Seafoam and Route 15 Gatehouse)
StaticPokemon{}={Species=[0x16C3DB, 0x16C3E2, 0x16C436], Level=[0x16C3DD]} // Moltres in Mt.Ember
StaticPokemon{}={Species=[0x168F71, 0x168F85, 0x168FCB], Level=[0x168F87]} // Mewtwo in Unk. Dungeon
StaticPokemon{}={Species=[0x1765A4, 0x1765AB, 0x164BE4, 0x164BEA], Level=[0x1765A6]} // Snorlax (Route 12 and S.S. Anne)
StaticPokemon{}={Species=[0x177612, 0x177619], Level=[0x177614]} // Snorlax (Route 16)
StaticPokemon{}={Species=[0x16C55F, 0x16C565], Level=[0x16C567]} // Hypno in Berry Forest
StaticPokemon{}={Species=[0x16EDDB, 0x16EDEB, 0x16EE35, 0x16EE40], Level=[0x16EDF0]} // Deoxys on Birth Island
StaticPokemon{}={Species=[0x16EB31, 0x16EB60, 0x16EBAF, 0x16EBBA], Level=[0x16EB65]} // Ho-Oh on Navel Rock
StaticPokemon{}={Species=[0x16EC7F, 0x16EC8A, 0x16ECD4, 0x16ECDF], Level=[0x16EC8F]} // Lugia on Navel Rock
StaticPokemon{}={Species=[0x189158, 0x18915C, 0x189167, 0x189059], Level=[0x189169]} // Old Amber
StaticPokemon{}={Species=[0x1890CE, 0x1890D2, 0x1890DD, 0x188FDD], Level=[0x1890DF]} // Helix Fossil
StaticPokemon{}={Species=[0x189113, 0x189117, 0x189122, 0x18901B], Level=[0x189124]} // Dome Fossil
StaticPokemon{}={Species=[0x167160, 0x167163, 0x1671A2, 0x1671D5], Level=[0x167165]} // Lapras in Silph. Co
StaticPokemon{}={Species=[0x18C221, 0x18C224, 0x18C2E3], Level=[0x18C226]} // Magikarp in Mt.Moon Center
StaticPokemon{}={Species=[0x185646, 0x1856C2], Level=[0x185705]} // Abra
StaticPokemon{}={Species=[0x185656, 0x1856CD], Level=[0x18571A]} // Clefairy
StaticPokemon{}={Species=[0x185676, 0x1856E3], Level=[0x185744]} // Scyther
StaticPokemon{}={Species=[0x185666, 0x1856D8], Level=[0x18572F]} // Dratini
StaticPokemon{}={Species=[0x185686, 0x1856EE], Level=[0x185759]} // Porygon
FossilLevelOffsets=[0x189169, 0x1890DF, 0x189124]
ShopItemOffsets=[0x16E2C4, 0x173C3C, 0x173C54, 0x173C70, 0x173C90, 0x17EB68, 0x17F9E4, 0x180C8C, 0x181DCC, 0x182900, 0x183504, 0x183540, 0x1838AC, 0x1839C4, 0x1839FC, 0x1869E4, 0x189684, 0x189778, 0x18ABD0, 0x18F050, 0x1910D4, 0x1919B8, 0x191F10]
CRC32=BB640DF7

[Leaf Green (J)]
Game=BPGJ
Version=0
Type=FRLG
CopyFrom=Fire Red (J)
PokemonMovesets=0x21A19C
EggMoves=0x21B8F8
PokemonTMHMCompat=0x20F5AC
PokemonEvolutions=0x21613C
StarterPokemon=0x17D1AC
TrainerData=0x1FDFB4
TrainerClassNames=0x1FDB18
TmMoves=0x419CBC
TmMovesDuplicate=0x419F24
MoveTutorData=0x419278
ItemImages=0x39C60C
TmPals=[0xD91F0C, 0xD91E6C, 0xD920C4, 0xD91F34, 0xD9204C, 0xD9209C, 0xD91F84, 0xD92074, 0xD91FFC, 0xD92074, 0xD91FAC, 0xD91EBC, 0xD91F84, 0xD92024, 0xD91EE4, 0xD91F5C, 0xD91E94, 0xD91FD4]
IntroCryOffset=0x130324
IntroSpriteOffset=0x13178C
IntroOtherOffset=0x131738
TradeTableOffset=0x22D2D8
RunIndoorsTweakOffset=0xBE728
TextSpeedValuesOffset=0x3E2F3C
CatchingTutorialOpponentMonOffset=0x7EF88
PCPotionOffset=0x3C8268
TypeEffectivenessOffset=0x20BF00
StaticPokemonSupport=1
StaticPokemon{}={Species=[0x184B94, 0x184B97, 0x184BD7, 0x184C0B], Level=[0x184B99]} // Eevee in Celadon Condominiums
StaticPokemon{}={Species=[0x18A2BC, 0x18A2C3], Level=[0x18A336]} // Hitmonlee in Fighting Dojo
StaticPokemon{}={Species=[0x18A302, 0x18A309], Level=[0x18A336]} // Hitmonchan in Fighting Dojo
StaticPokemon{}={Species=[0x16C167, 0x16C16E], Level=[0x16C169]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x16C1C5, 0x16C1CC], Level=[0x16C1C7]} // Electrode in The Power Plant
StaticPokemon{}={Species=[0x16C0F3, 0x16C0FA, 0x16C14E], Level=[0x16C0F5]} // Zapdos in The Power Plant
StaticPokemon{}={Species=[0x16B294, 0x16B29B, 0x16B2EF, 0x18DE5E, 0x18DE6A], Level=[0x16B296]} // Articuno (Seafoam and Route 15 Gatehouse)
StaticPokemon{}={Species=[0x16C822, 0x16C829, 0x16C87D], Level=[0x16C824]} // Moltres in Mt.Ember
StaticPokemon{}={Species=[0x1693BD, 0x1693D1, 0x169417], Level=[0x1693D3]} // Mewtwo in Unk. Dungeon
StaticPokemon{}={Species=[0x1769CC, 0x1769D3, 0x16503A, 0x165040], Level=[0x1769CE]} // Snorlax (Route 12 and S.S. Anne)
StaticPokemon{}={Species=[0x177A3A, 0x177A41], Level=[0x177A3C]} // Snorlax (Route 16)
StaticPokemon{}={Species=[0x16C9A6, 0x16C9AC], Level=[0x16C9AE]} // Hypno in Berry Forest
StaticPokemon{}={Species=[0x16F21F, 0x16F22F, 0x16F279, 0x16F284], Level=[0x16F234]} // Deoxys on Birth Island
StaticPokemon{}={Species=[0x16EF75, 0x16EFA4, 0x16EFF3, 0x16EFFE], Level=[0x16EFA9]} // Ho-Oh on Navel Rock
StaticPokemon{}={Species=[0x16F0C3, 0x16F0CE, 0x16F118, 0x16F123], Level=[0x16F0D3]} // Lugia on Navel Rock
StaticPokemon{}={Species=[0x189578, 0x18957C, 0x189587, 0x189479], Level=[0x189589]} // Old Amber
StaticPokemon{}={Species=[0x1894EE, 0x1894F2, 0x1894FD, 0x1893FD], Level=[0x1894FF]} // Helix Fossil
StaticPokemon{}={Species=[0x189533, 0x189537, 0x189542, 0x18943B], Level=[0x189544]} // Dome Fossil
StaticPokemon{}={Species=[0x1675B1, 0x1675B4, 0x1675F3, 0x167626], Level=[0x1675B6]} // Lapras in Silph. Co
StaticPokemon{}={Species=[0x18C641, 0x18C644, 0x18C703], Level=[0x18C646]} // Magikarp in Mt.Moon Center
StaticPokemon{}={Species=[0x185A66, 0x185AE2], Level=[0x185B25]} // Abra
StaticPokemon{}={Species=[0x185A76, 0x185AED], Level=[0x185B3A]} // Clefairy
StaticPokemon{}={Species=[0x185A86, 0x185B19], Level=[0x185B8E]} // Pinsir
StaticPokemon{}={Species=[0x185A96, 0x185AF8], Level=[0x185B4F]} // Dratini
StaticPokemon{}={Species=[0x185AA6, 0x185B0E], Level=[0x185B79]} // Porygon
FossilLevelOffsets=[0x189589, 0x1894FF, 0x189544]
ShopItemOffsets=[0x16E708, 0x174078, 0x174090, 0x1740AC, 0x1740CC, 0x17EF88, 0x17FE04, 0x1810AC, 0x1821EC, 0x182D20, 0x183924, 0x183960, 0x183CCC, 0x183DE4, 0x183E1C, 0x186E04, 0x189AA4, 0x189B98, 0x18AFF0, 0x18F470, 0x1914F4, 0x191DD8, 0x192330]
CRC32=0A48556B
